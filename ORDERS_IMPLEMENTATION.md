# Orders Page Implementation

This document describes the implementation of the orders page that shows user's orders and their details.

## Features Implemented

### ✅ **Orders Page (`/orders`)**
- Displays user's order history with pagination
- Shows order status, amounts, currencies, and dates
- Expandable order cards with detailed information
- Responsive design with mobile support
- Dark mode support

### ✅ **User Menu Integration**
- Added "سفارشات" (Orders) menu item to user dropdown
- Positioned between "پروفایل" (Profile) and "کارت‌های بانکی" (Bank Cards)

### ✅ **Order Management**
- Users can view their orders but cannot delete them (as requested)
- Order details include:
  - Order ID (last 8 characters displayed)
  - Status with color coding
  - Exchange details (from/to currencies and amounts)
  - Exchange rate
  - Bank card information
  - Deposit ID (if provided)
  - Creation and update timestamps

### ✅ **Order Status System**
- `pending` - در انتظار (Orange)
- `processing` - در حال پردازش (Blue)
- `completed` - تکمیل شده (Green)
- `cancelled` - لغو شده (Red)
- `failed` - ناموفق (Red)

## Files Created/Modified

### New Files
- `src/app/orders/page.tsx` - Orders page component
- `src/app/orders/page.module.css` - Orders page styles
- `src/components/OrdersList.tsx` - Orders list component
- `src/components/OrdersList.module.css` - Orders list styles
- `src/components/OrderCard.tsx` - Individual order card component
- `src/components/OrderCard.module.css` - Order card styles
- `src/__tests__/OrdersList.test.tsx` - Tests for orders list

### Modified Files
- `src/types/order.ts` - Added order list and details interfaces
- `src/services/orderService.ts` - Added methods to fetch orders
- `src/components/Header.tsx` - Added orders menu item
- `src/components/OrderForm.tsx` - Added redirect to orders after submission

## API Integration

### Order Service Methods
```typescript
// Get user's orders with pagination
OrderService.getUserOrders(token, page, limit)

// Get specific order details
OrderService.getOrderById(orderId, token)
```

### Expected API Endpoints
- `GET /orders?page=1&limit=10` - Get user's orders with pagination
- `GET /orders/{orderId}` - Get specific order details

### Expected API Response Format
```typescript
// Orders List Response
{
  success: boolean;
  message: string;
  data: {
    orders: Order[];
    total: number;
    page: number;
    limit: number;
  };
}

// Order Details Response
{
  success: boolean;
  message: string;
  data: Order;
}
```

## Order Data Structure

```typescript
interface Order {
  id: string;
  cardId: string;
  fromCurrency: string;
  toCurrency: string;
  fromAmount: number;
  toAmount: number;
  exchangeRate: number;
  depositId?: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled' | 'failed';
  createdAt: string;
  updatedAt: string;
  // Optional additional fields from API
  cardInfo?: {
    bankName: string;
    cardNumber: string; // Last 4 digits
  };
  currencyInfo?: {
    fromCurrencyFa: string;
    toCurrencyFa: string;
  };
}
```

## User Experience Flow

1. **Accessing Orders**
   - User clicks on their profile dropdown in header
   - Selects "سفارشات" (Orders) from menu
   - Redirected to `/orders` page

2. **Viewing Orders**
   - Orders displayed as cards with summary information
   - Click expand button (▼) to see full details
   - Pagination with "Load More" button for additional orders

3. **Order Information Display**
   - **Summary**: Order number, status, date, exchange summary
   - **Details**: Exchange rate, bank card, deposit ID, timestamps

4. **After Order Submission**
   - User submits order from `/order` page
   - Success message shown
   - Automatically redirected to `/orders` page to view new order

## Responsive Design

### Desktop
- Orders displayed in single column with full details
- Expandable cards with grid layout for details

### Mobile
- Compact card layout
- Stacked exchange information
- Single column detail grid
- Touch-friendly expand/collapse buttons

## Styling Features

### Light Mode
- Clean white cards with subtle shadows
- Red accent color (#d32f2f) for branding
- Status badges with appropriate colors

### Dark Mode
- Dark background with light text
- Red accent color adjusted to #ff6b6b
- Consistent with overall app theme

## Error Handling

- Loading states during data fetch
- Error messages for API failures
- Empty state when no orders exist
- Graceful handling of missing data

## Testing

- Unit tests for OrdersList component
- Mocked API responses for testing
- Tests for empty state and error conditions

## Future Enhancements

Potential features that could be added:
- Order filtering by status or date range
- Order search functionality
- Export orders to PDF/CSV
- Order tracking with detailed status updates
- Push notifications for status changes
