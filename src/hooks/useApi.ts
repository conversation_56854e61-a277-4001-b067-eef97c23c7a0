import { useAuth } from '@/contexts/AuthContext';
import { createApiInstance } from '@/config/api';
import { useState } from 'react';

export const useApi = () => {
  const { token } = useAuth();
  const [errorMessages, setErrorMessages] = useState<string[] | null>(null);

  const api = createApiInstance(token || undefined, (messages) => {
    setErrorMessages(messages);
  });

  // Return api instance and error state/handler
  return {
    api,
    errorMessages,
    clearErrorMessages: () => setErrorMessages(null),
  };
};
