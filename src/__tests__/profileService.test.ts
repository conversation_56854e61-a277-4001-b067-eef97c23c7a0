import { ProfileService } from '@/services/profileService';
import { API_CONFIG } from '@/config/api';

// Mock fetch globally
global.fetch = jest.fn();

describe('ProfileService', () => {
  const mockToken = 'mock-bearer-token';
  
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getProfile', () => {
    it('should fetch profile successfully', async () => {
      const mockResponse = {
        success: true,
        message: 'Profile fetched successfully',
        data: {
          name: 'احمد',
          familyName: 'محمدی',
          nationalCode: '1234567890',
          state: 'تهران',
          city: 'تهران',
          address: 'خیابان ولیعصر',
          nationalCardImagePath: '/images/national-card.jpg',
          authImagePath: '/images/auth.jpg',
        }
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await ProfileService.getProfile(mockToken);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(API_CONFIG.endpoints.profile),
        {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${mockToken}`,
          },
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle API errors', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 401,
      });

      await expect(
        ProfileService.getProfile(mockToken)
      ).rejects.toThrow('Failed to fetch profile. Please try again.');
    });
  });

  describe('updateProfile', () => {
    it('should update profile successfully', async () => {
      const profileData = {
        name: 'احمد',
        familyName: 'محمدی',
        nationalCode: '1234567890',
        state: 'تهران',
        city: 'تهران',
        address: 'خیابان ولیعصر',
        nationalCardImagePath: '/images/national-card.jpg',
        authImagePath: '/images/auth.jpg',
      };

      const mockResponse = {
        success: true,
        message: 'Profile updated successfully',
        data: profileData
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await ProfileService.updateProfile(profileData, mockToken);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining(API_CONFIG.endpoints.profile),
        {
          method: 'PATCH',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${mockToken}`,
          },
          body: JSON.stringify(profileData),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle update errors', async () => {
      const profileData = {
        name: 'احمد',
        familyName: 'محمدی',
        nationalCode: '1234567890',
        state: 'تهران',
        city: 'تهران',
        address: 'خیابان ولیعصر',
        nationalCardImagePath: '/images/national-card.jpg',
        authImagePath: '/images/auth.jpg',
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 400,
      });

      await expect(
        ProfileService.updateProfile(profileData, mockToken)
      ).rejects.toThrow('Failed to update profile. Please try again.');
    });
  });

  describe('uploadImage', () => {
    it('should upload image successfully', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const mockResponse = {
        success: true,
        message: 'Image uploaded successfully',
        path: '/uploads/images/test.jpg'
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const result = await ProfileService.uploadImage(mockFile, mockToken);

      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('/upload/image'),
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${mockToken}`,
          },
          body: expect.any(FormData),
        }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle upload errors', async () => {
      const mockFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
      });

      await expect(
        ProfileService.uploadImage(mockFile, mockToken)
      ).rejects.toThrow('Failed to upload image. Please try again.');
    });
  });
});
