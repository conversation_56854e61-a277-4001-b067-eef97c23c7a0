/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CurrencyPrices from '@/components/CurrencyPrices';
import { CurrencyService } from '@/services/currencyService';

// Mock the CurrencyService
jest.mock('@/services/currencyService');
const mockCurrencyService = CurrencyService as jest.Mocked<typeof CurrencyService>;

const mockCurrencies = [
  {
    _id: "6842f858fb5c71e0b34fd550",
    name: "toman",
    sellPrice: 1,
    buyPrice: 1,
    fa: "تومان"
  },
  {
    _id: "6842f858fb5c71e0b34fd551",
    name: "lira",
    sellPrice: 2140,
    buyPrice: 2100,
    fa: "لیر"
  },
  {
    _id: "6842f858fb5c71e0b34fd552",
    name: "dollar",
    sellPrice: 42000,
    buyPrice: 41500,
    fa: "دلار"
  }
];

describe('CurrencyPrices', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should render loading state initially', () => {
    mockCurrencyService.getCurrencies.mockImplementation(() => new Promise(() => {}));
    
    render(<CurrencyPrices />);
    
    expect(screen.getByText('قیمت لحظه‌ای ارزها')).toBeInTheDocument();
    expect(screen.getByText('در حال بارگذاری قیمت ارزها...')).toBeInTheDocument();
  });

  it('should render currency prices after loading', async () => {
    mockCurrencyService.getCurrencies.mockResolvedValue(mockCurrencies);
    
    render(<CurrencyPrices />);
    
    // Wait for currencies to load
    await waitFor(() => {
      expect(screen.getByText('لیر')).toBeInTheDocument();
    });

    // Check if currencies are displayed (excluding toman)
    expect(screen.getByText('لیر')).toBeInTheDocument();
    expect(screen.getByText('دلار')).toBeInTheDocument();
    expect(screen.queryByText('تومان')).not.toBeInTheDocument(); // toman should be filtered out

    // Check if prices are displayed (Persian numbers)
    expect(screen.getByText('۲٬۱۰۰ تومان')).toBeInTheDocument(); // lira buy price
    expect(screen.getByText('۲٬۱۴۰ تومان')).toBeInTheDocument(); // lira sell price
    expect(screen.getByText('۴۱٬۵۰۰ تومان')).toBeInTheDocument(); // dollar buy price
    expect(screen.getByText('۴۲٬۰۰۰ تومان')).toBeInTheDocument(); // dollar sell price
  });

  it('should render error state when API fails', async () => {
    const errorMessage = 'Failed to fetch currencies';
    mockCurrencyService.getCurrencies.mockRejectedValue(new Error(errorMessage));
    
    render(<CurrencyPrices />);
    
    await waitFor(() => {
      expect(screen.getByText('Failed to fetch currencies')).toBeInTheDocument();
    });

    expect(screen.getByText('تلاش مجدد')).toBeInTheDocument();
  });

  it('should display refresh note', async () => {
    mockCurrencyService.getCurrencies.mockResolvedValue(mockCurrencies);
    
    render(<CurrencyPrices />);
    
    await waitFor(() => {
      expect(screen.getByText('قیمت‌ها هر ۳۰ ثانیه به‌روزرسانی می‌شوند')).toBeInTheDocument();
    });
  });
});
