import {
  validateMobileNumber,
  validateOtp,
  formatMobileNumber,
  validateName,
  validateFamilyName,
  validateNationalCode,
  validateState,
  validateCity,
  validateAddress,
  validateImageFile
} from '@/utils/validation';

describe('Validation Utils', () => {
  describe('validateMobileNumber', () => {
    it('should validate correct Iranian mobile numbers', () => {
      const validNumbers = [
        '09123456789',
        '09901234567',
        '09351234567',
      ];

      validNumbers.forEach(number => {
        const result = validateMobileNumber(number);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid mobile numbers', () => {
      const invalidNumbers = [
        '',
        '123456789',
        '0912345678', // too short
        '091234567890', // too long
        '08123456789', // wrong prefix
        '9123456789', // missing 0
        'abc123456789', // contains letters
      ];

      invalidNumbers.forEach(number => {
        const result = validateMobileNumber(number);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });

    it('should handle mobile numbers with spaces and dashes', () => {
      const result = validateMobileNumber('0912 345 6789');
      expect(result.isValid).toBe(true);
      
      const result2 = validateMobileNumber('0912-345-6789');
      expect(result2.isValid).toBe(true);
    });
  });

  describe('validateOtp', () => {
    it('should validate correct OTP codes', () => {
      const validOtps = [
        '1234',
        '123456',
        '000000',
        '999999',
      ];

      validOtps.forEach(otp => {
        const result = validateOtp(otp);
        expect(result.isValid).toBe(true);
        expect(result.error).toBeUndefined();
      });
    });

    it('should reject invalid OTP codes', () => {
      const invalidOtps = [
        '',
        '123', // too short
        '1234567', // too long
        'abcd', // contains letters
        '12 34', // contains spaces (after cleaning should be valid)
      ];

      // Note: '12 34' should be valid after cleaning spaces
      const shouldBeValid = ['12 34'];
      const shouldBeInvalid = invalidOtps.filter(otp => !shouldBeValid.includes(otp));

      shouldBeInvalid.forEach(otp => {
        const result = validateOtp(otp);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });

      // Test the space case separately
      const spaceResult = validateOtp('12 34');
      expect(spaceResult.isValid).toBe(true);
    });
  });

  describe('formatMobileNumber', () => {
    it('should format 11-digit mobile numbers correctly', () => {
      const result = formatMobileNumber('09123456789');
      expect(result).toBe('0912 345 6789');
    });

    it('should return original string for non-11-digit numbers', () => {
      const shortNumber = '0912345678';
      expect(formatMobileNumber(shortNumber)).toBe(shortNumber);
      
      const longNumber = '091234567890';
      expect(formatMobileNumber(longNumber)).toBe(longNumber);
    });

    it('should handle already formatted numbers', () => {
      const formatted = '0912 345 6789';
      const result = formatMobileNumber(formatted);
      expect(result).toBe('0912 345 6789');
    });
  });

  describe('Profile validation functions', () => {
    describe('validateName', () => {
      it('should validate valid names', () => {
        expect(validateName('احمد')).toBeNull();
        expect(validateName('محمد علی')).toBeNull();
      });

      it('should reject empty names', () => {
        expect(validateName('')).toBe('نام الزامی است');
        expect(validateName('   ')).toBe('نام الزامی است');
      });

      it('should reject short names', () => {
        expect(validateName('ا')).toBe('نام باید حداقل ۲ کاراکتر باشد');
      });

      it('should reject long names', () => {
        const longName = 'ا'.repeat(51);
        expect(validateName(longName)).toBe('نام نباید بیش از ۵۰ کاراکتر باشد');
      });
    });

    describe('validateFamilyName', () => {
      it('should validate valid family names', () => {
        expect(validateFamilyName('محمدی')).toBeNull();
        expect(validateFamilyName('حسن زاده')).toBeNull();
      });

      it('should reject empty family names', () => {
        expect(validateFamilyName('')).toBe('نام خانوادگی الزامی است');
      });
    });

    describe('validateNationalCode', () => {
      it('should allow empty national code (optional)', () => {
        expect(validateNationalCode('')).toBeNull();
        expect(validateNationalCode('   ')).toBeNull();
      });

      it('should validate valid national codes', () => {
        expect(validateNationalCode('0123456789')).toBeNull(); // Valid format
      });

      it('should reject invalid format', () => {
        expect(validateNationalCode('123')).toBe('کد ملی باید ۱۰ رقم باشد');
        expect(validateNationalCode('12345678901')).toBe('کد ملی باید ۱۰ رقم باشد');
        expect(validateNationalCode('123456789a')).toBe('کد ملی باید ۱۰ رقم باشد');
      });
    });

    describe('validateState', () => {
      it('should validate valid states', () => {
        expect(validateState('تهران')).toBeNull();
        expect(validateState('اصفهان')).toBeNull();
      });

      it('should reject empty states', () => {
        expect(validateState('')).toBe('استان الزامی است');
      });

      it('should reject short states', () => {
        expect(validateState('ا')).toBe('نام استان باید حداقل ۲ کاراکتر باشد');
      });
    });

    describe('validateCity', () => {
      it('should validate valid cities', () => {
        expect(validateCity('تهران')).toBeNull();
        expect(validateCity('شیراز')).toBeNull();
      });

      it('should reject empty cities', () => {
        expect(validateCity('')).toBe('شهر الزامی است');
      });
    });

    describe('validateAddress', () => {
      it('should validate valid addresses', () => {
        expect(validateAddress('خیابان ولیعصر، پلاک ۱۲۳')).toBeNull();
      });

      it('should reject empty addresses', () => {
        expect(validateAddress('')).toBe('آدرس الزامی است');
      });

      it('should reject short addresses', () => {
        expect(validateAddress('کوتاه')).toBe('آدرس باید حداقل ۱۰ کاراکتر باشد');
      });

      it('should reject very long addresses', () => {
        const longAddress = 'ا'.repeat(501);
        expect(validateAddress(longAddress)).toBe('آدرس نباید بیش از ۵۰۰ کاراکتر باشد');
      });
    });

    describe('validateImageFile', () => {
      it('should reject null file', () => {
        expect(validateImageFile(null)).toBe('تصویر الزامی است');
      });

      it('should validate valid image files', () => {
        const validFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
        expect(validateImageFile(validFile)).toBeNull();
      });

      it('should reject invalid file types', () => {
        const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
        expect(validateImageFile(invalidFile)).toBe('فرمت تصویر باید JPG یا PNG باشد');
      });

      it('should reject large files', () => {
        const largeFile = new File(['x'.repeat(6 * 1024 * 1024)], 'large.jpg', { type: 'image/jpeg' });
        expect(validateImageFile(largeFile)).toBe('حجم تصویر نباید بیش از ۵ مگابایت باشد');
      });
    });
  });
});
