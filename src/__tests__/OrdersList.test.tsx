/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import OrdersList from '@/components/OrdersList';

// Mock the dependencies
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    token: 'mock-token',
    isAuthenticated: true,
  }),
}));

jest.mock('@/services/orderService', () => ({
  OrderService: {
    getUserOrders: jest.fn().mockResolvedValue({
      success: true,
      message: 'Orders fetched successfully',
      data: {
        orders: [
          {
            id: 'order-123',
            cardId: 'card-1',
            fromCurrency: 'dollar',
            toCurrency: 'toman',
            fromAmount: 100,
            toAmount: 4200000,
            exchangeRate: 42000,
            status: 'completed',
            createdAt: '2024-01-01T00:00:00Z',
            updatedAt: '2024-01-01T00:00:00Z',
            cardInfo: {
              bankName: 'ملی',
              cardNumber: '1234',
            },
          },
        ],
        total: 1,
        page: 1,
        limit: 10,
      },
    }),
  },
}));

jest.mock('@/services/currencyService', () => ({
  CurrencyService: {
    getCurrencies: jest.fn().mockResolvedValue([
      {
        _id: '1',
        name: 'dollar',
        sellPrice: 42000,
        buyPrice: 41500,
        fa: 'دلار',
      },
      {
        _id: '2',
        name: 'toman',
        sellPrice: 1,
        buyPrice: 1,
        fa: 'تومان',
      },
    ]),
  },
}));

describe('OrdersList', () => {
  it('should render orders list', async () => {
    render(<OrdersList />);
    
    // Wait for the component to load
    await screen.findByText(/سفارش #/);
    
    // Check if order information is displayed
    expect(screen.getByText(/سفارش #/)).toBeInTheDocument();
    expect(screen.getByText('تکمیل شده')).toBeInTheDocument();
    expect(screen.getByText(/100.*دلار/)).toBeInTheDocument();
    expect(screen.getByText(/4,200,000.*تومان/)).toBeInTheDocument();
  });

  it('should show empty state when no orders', async () => {
    // Mock empty orders response
    const mockOrderService = require('@/services/orderService');
    mockOrderService.OrderService.getUserOrders.mockResolvedValueOnce({
      success: true,
      data: {
        orders: [],
        total: 0,
        page: 1,
        limit: 10,
      },
    });

    render(<OrdersList />);
    
    // Wait for the component to load
    await screen.findByText('هیچ سفارشی یافت نشد');
    
    // Check if empty state is displayed
    expect(screen.getByText('هیچ سفارشی یافت نشد')).toBeInTheDocument();
    expect(screen.getByText('شما هنوز هیچ سفارش تبدیل ارزی ثبت نکرده‌اید.')).toBeInTheDocument();
    expect(screen.getByText('ثبت سفارش جدید')).toBeInTheDocument();
  });
});
