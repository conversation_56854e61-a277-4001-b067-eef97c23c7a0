/**
 * @jest-environment jsdom
 */

import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import OrderForm from '@/components/OrderForm';

// Mock the dependencies
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    token: 'mock-token',
    isAuthenticated: true,
  }),
}));

jest.mock('next/navigation', () => ({
  useSearchParams: () => ({
    get: (key: string) => {
      const mockParams: Record<string, string> = {
        fromCurrency: 'dollar',
        toCurrency: 'toman',
        fromAmount: '100',
        toAmount: '4200000',
        fromCurrencyFa: 'دلار',
        toCurrencyFa: 'تومان',
      };
      return mockParams[key] || null;
    },
  }),
}));

jest.mock('@/services/bankCardService', () => ({
  getBankCards: jest.fn().mockResolvedValue([
    {
      id: '1',
      cardNumber: '****************',
      bankName: 'ملی',
      accountHolderName: 'احمد محمدی',
      iban: 'IR****************78901234',
    },
  ]),
}));

jest.mock('@/services/currencyService', () => ({
  CurrencyService: {
    getCurrencies: jest.fn().mockResolvedValue([
      {
        _id: '1',
        name: 'dollar',
        sellPrice: 42000,
        buyPrice: 41500,
        fa: 'دلار',
      },
      {
        _id: '2',
        name: 'toman',
        sellPrice: 1,
        buyPrice: 1,
        fa: 'تومان',
      },
    ]),
  },
}));

jest.mock('@/services/orderService', () => ({
  OrderService: {
    submitOrder: jest.fn().mockResolvedValue({
      success: true,
      message: 'Order submitted successfully',
      data: {
        id: 'order-123',
        status: 'pending',
        createdAt: '2024-01-01T00:00:00Z',
      },
    }),
  },
}));

describe('OrderForm', () => {
  it('should render exchange details table', async () => {
    render(<OrderForm />);
    
    // Wait for the component to load
    await screen.findByText('جزئیات تبدیل ارز');
    
    // Check if exchange details are displayed
    expect(screen.getByText('از ارز:')).toBeInTheDocument();
    expect(screen.getByText('به ارز:')).toBeInTheDocument();
    expect(screen.getByText('مبلغ پرداختی:')).toBeInTheDocument();
    expect(screen.getByText('مبلغ دریافتی:')).toBeInTheDocument();
    expect(screen.getByText('نرخ تبدیل:')).toBeInTheDocument();
  });

  it('should show card selection when user has cards', async () => {
    render(<OrderForm />);
    
    // Wait for the component to load
    await screen.findByText('اطلاعات سفارش');
    
    // Check if card selection is displayed
    expect(screen.getByText('انتخاب کارت بانکی *')).toBeInTheDocument();
    expect(screen.getByText('کارت انتخاب شده:')).toBeInTheDocument();
    expect(screen.getByText('ملی')).toBeInTheDocument();
  });

  it('should have submit button', async () => {
    render(<OrderForm />);
    
    // Wait for the component to load
    await screen.findByText('اطلاعات سفارش');
    
    // Check if submit button is present
    expect(screen.getByRole('button', { name: 'ثبت سفارش' })).toBeInTheDocument();
  });
});
