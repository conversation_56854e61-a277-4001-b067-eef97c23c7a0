/**
 * Validation utilities for forms
 */

/**
 * Validate Iranian mobile number
 */
export const validateMobileNumber = (mobile: string): { isValid: boolean; error?: string } => {
  // Remove any spaces or dashes
  const cleanMobile = mobile.replace(/[\s-]/g, '');
  
  // Check if it's empty
  if (!cleanMobile) {
    return { isValid: false, error: 'شماره موبایل الزامی است' };
  }
  
  // Iranian mobile number pattern (09xxxxxxxxx)
  const iranMobilePattern = /^09[0-9]{9}$/;
  
  if (!iranMobilePattern.test(cleanMobile)) {
    return { isValid: false, error: 'شماره موبایل معتبر نیست (مثال: 09123456789)' };
  }
  
  return { isValid: true };
};

/**
 * Validate OTP code
 */
export const validateOtp = (otp: string): { isValid: boolean; error?: string } => {
  // Remove any spaces
  const cleanOtp = otp.replace(/\s/g, '');
  
  // Check if it's empty
  if (!cleanOtp) {
    return { isValid: false, error: 'کد تأیید الزامی است' };
  }
  
  // OTP should be 4-6 digits
  const otpPattern = /^[0-9]{4,6}$/;
  
  if (!otpPattern.test(cleanOtp)) {
    return { isValid: false, error: 'کد تأیید باید ۴ تا ۶ رقم باشد' };
  }
  
  return { isValid: true };
};

/**
 * Format mobile number for display (add spaces for readability)
 */
export const formatMobileNumber = (mobile: string): string => {
  const cleanMobile = mobile.replace(/[\s-]/g, '');
  if (cleanMobile.length === 11) {
    return `${cleanMobile.slice(0, 4)} ${cleanMobile.slice(4, 7)} ${cleanMobile.slice(7)}`;
  }
  return mobile;
};

// Profile validation functions
export const validateName = (name: string): string | null => {
  if (!name || !name.trim()) {
    return 'نام الزامی است';
  }

  if (name.trim().length < 2) {
    return 'نام باید حداقل ۲ کاراکتر باشد';
  }

  if (name.trim().length > 50) {
    return 'نام نباید بیش از ۵۰ کاراکتر باشد';
  }

  return null;
};

export const validateFamilyName = (familyName: string): string | null => {
  if (!familyName || !familyName.trim()) {
    return 'نام خانوادگی الزامی است';
  }

  if (familyName.trim().length < 2) {
    return 'نام خانوادگی باید حداقل ۲ کاراکتر باشد';
  }

  if (familyName.trim().length > 50) {
    return 'نام خانوادگی نباید بیش از ۵۰ کاراکتر باشد';
  }

  return null;
};

export const validateNationalCode = (nationalCode: string): string | null => {
  if (!nationalCode || !nationalCode.trim()) {
    return null; // Optional field
  }

  const code = nationalCode.trim();

  if (!/^\d{10}$/.test(code)) {
    return 'کد ملی باید ۱۰ رقم باشد';
  }

  // Iranian national code validation algorithm
  const check = parseInt(code[9]);
  let sum = 0;

  for (let i = 0; i < 9; i++) {
    sum += parseInt(code[i]) * (10 - i);
  }

  const remainder = sum % 11;
  const isValid = (remainder < 2 && check === remainder) || (remainder >= 2 && check === 11 - remainder);

  if (!isValid) {
    return 'کد ملی نامعتبر است';
  }

  return null;
};

export const validateState = (state: string): string | null => {
  if (!state || !state.trim()) {
    return 'استان الزامی است';
  }

  if (state.trim().length < 2) {
    return 'نام استان باید حداقل ۲ کاراکتر باشد';
  }

  return null;
};

export const validateCity = (city: string): string | null => {
  if (!city || !city.trim()) {
    return 'شهر الزامی است';
  }

  if (city.trim().length < 2) {
    return 'نام شهر باید حداقل ۲ کاراکتر باشد';
  }

  return null;
};

export const validateAddress = (address: string): string | null => {
  if (!address || !address.trim()) {
    return 'آدرس الزامی است';
  }

  if (address.trim().length < 10) {
    return 'آدرس باید حداقل ۱۰ کاراکتر باشد';
  }

  if (address.trim().length > 500) {
    return 'آدرس نباید بیش از ۵۰۰ کاراکتر باشد';
  }

  return null;
};

export const validateImageFile = (file: File | null): string | null => {
  if (!file) {
    return 'تصویر الزامی است';
  }

  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png'];
  if (!allowedTypes.includes(file.type)) {
    return 'فرمت تصویر باید JPG یا PNG باشد';
  }

  const maxSize = 5 * 1024 * 1024; // 5MB
  if (file.size > maxSize) {
    return 'حجم تصویر نباید بیش از ۵ مگابایت باشد';
  }

  return null;
};
