.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa; /* Light gray background */
  color: #343a40; /* Dark text color */
}

.mainContent {
  flex: 1;
  padding: 40px 20px;
  max-width: 900px;
  margin: 0 auto;
  background-color: #ffffff; /* White content background */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  line-height: 1.8;
}

.title {
  font-size: 2.8rem;
  color: #007bff; /* Primary blue color */
  text-align: center;
  margin-bottom: 30px;
  font-weight: bold;
  position: relative;
}

.title::after {
  content: '';
  display: block;
  width: 80px;
  height: 4px;
  background-color: #007bff;
  margin: 15px auto 0;
  border-radius: 2px;
}

.subtitle {
  font-size: 2rem;
  color: #0056b3; /* Slightly darker blue */
  margin-top: 40px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 10px;
}

.description {
  font-size: 1.1rem;
  margin-bottom: 20px;
  text-align: justify;
}

.valuesList {
  list-style: none;
  padding: 0;
  margin-top: 20px;
}

.valuesList li {
  background-color: #eaf6ff; /* Light blue for list items */
  margin-bottom: 10px;
  padding: 15px 20px;
  border-left: 5px solid #007bff;
  border-radius: 5px;
  font-size: 1.05rem;
  transition: transform 0.2s ease-in-out;
}

.valuesList li:hover {
  transform: translateX(5px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mainContent {
    padding: 30px 15px;
    margin: 20px;
  }

  .title {
    font-size: 2.2rem;
  }

  .subtitle {
    font-size: 1.7rem;
  }

  .description {
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .mainContent {
    padding: 20px 10px;
    margin: 10px;
  }

  .title {
    font-size: 1.8rem;
  }

  .subtitle {
    font-size: 1.5rem;
  }
}
