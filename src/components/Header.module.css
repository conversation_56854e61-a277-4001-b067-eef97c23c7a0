.header {
  background: #fff;
  border-bottom: 1px solid #e5e5e5;
  padding: 1rem 0;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo h1 {
  color: #d32f2f;
  font-size: 1.8rem;
  font-weight: bold;
  margin: 0;
}

.nav ul {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 2rem;
}

.nav a,
.nav a:visited {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s;
}

.nav a:hover {
  color: #d32f2f;
}

.authSection {
  display: flex;
  align-items: center;
}

.userMenu {
  position: relative;
  display: inline-block;
}

.userMenuTrigger {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: transparent;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: all 0.2s;
  font-family: inherit;
}

.userMenuTrigger:hover {
  background: #f5f5f5;
  border-color: #d32f2f;
}

.userMenuTrigger:focus {
  outline: none;
  border-color: #d32f2f;
  box-shadow: 0 0 0 2px rgba(211, 47, 47, 0.1);
}

.userInfo {
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.dropdownIcon {
  transition: transform 0.2s;
  color: #666;
}

.dropdownIconOpen {
  transform: rotate(180deg);
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 180px;
  z-index: 1000;
  margin-top: 0.25rem;
  overflow: hidden;
  animation: dropdownFadeIn 0.2s ease-out;
}

@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.dropdownItem {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  border: none;
  background: transparent;
  text-align: right;
  cursor: pointer;
  transition: background-color 0.2s;
  font-family: inherit;
}

.dropdownItem:hover {
  background: #f5f5f5;
}

.dropdownItem:focus {
  outline: none;
  background: #f5f5f5;
}

.logoutItem {
  color: #d32f2f;
  border-top: 1px solid #e5e5e5;
}

.logoutItem:hover {
  background: #fef2f2;
}

.profileLink {
  background: #4caf50;
  color: white;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.85rem;
  transition: all 0.2s;
}

.profileLink:hover {
  background: #388e3c;
  transform: translateY(-1px);
}

.loginBtn {
  background: #d32f2f;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.2s;
}

.loginBtn:hover {
  background: #b71c1c;
  transform: translateY(-1px);
}

.logoutBtn {
  background: transparent;
  color: #d32f2f;
  border: 1px solid #d32f2f;
  padding: 0.4rem 0.8rem;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
}

.logoutBtn:hover {
  background: #d32f2f;
  color: white;
}

/* Hamburger Menu Styles */
.hamburgerBtn {
  display: none;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 40px;
  height: 40px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.hamburgerBtn:hover {
  background: #f5f5f5;
}

.hamburgerBtn:focus {
  outline: none;
  background: #f5f5f5;
}

.hamburgerLine {
  width: 24px;
  height: 2px;
  background: #333;
  transition: all 0.3s ease;
  transform-origin: center;
}

.hamburgerLine:not(:last-child) {
  margin-bottom: 4px;
}

.hamburgerLineOpen1 {
  transform: rotate(45deg) translate(5px, 5px);
}

.hamburgerLineOpen2 {
  opacity: 0;
}

.hamburgerLineOpen3 {
  transform: rotate(-45deg) translate(7px, -6px);
}

/* Mobile Menu Overlay */
.mobileMenuOverlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.mobileMenu {
  position: fixed;
  top: 0;
  right: 0;
  height: 100vh;
  width: 280px;
  background: white;
  box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
  transform: translateX(100%);
  animation: slideInRight 0.3s ease-out forwards;
  overflow-y: auto;
}

@keyframes slideInRight {
  to {
    transform: translateX(0);
  }
}

.mobileMenuHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-bottom: 1px solid #e5e5e5;
  background: #f8f9fa;
}

.mobileMenuHeader h2 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.mobileMenuClose {
  background: transparent;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  padding: 0.25rem;
  color: #666;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.mobileMenuClose:hover {
  background: #e9ecef;
}

.mobileNav {
  padding: 1rem 0;
}

.mobileNav ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

.mobileNav li {
  border-bottom: 1px solid #f0f0f0;
}

.mobileNav a {
  display: block;
  padding: 1rem;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.2s;
}

.mobileNav a:hover {
  background: #f8f9fa;
  color: #d32f2f;
}

.mobileAuthSection {
  padding: 1rem;
  border-top: 1px solid #e5e5e5;
  margin-top: auto;
}

.mobileUserMenu {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.mobileUserInfo {
  font-weight: 600;
  color: #333;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 8px;
  text-align: center;
}

.mobileUserActions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.mobileMenuItem {
  display: block;
  width: 100%;
  padding: 0.75rem;
  color: #333;
  text-decoration: none;
  font-weight: 500;
  border: none;
  background: transparent;
  text-align: right;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s;
  font-family: inherit;
}

.mobileMenuItem:hover {
  background: #f8f9fa;
}

.mobileLogoutItem {
  color: #d32f2f;
  border-top: 1px solid #e5e5e5;
  margin-top: 0.5rem;
  padding-top: 1rem;
}

.mobileLogoutItem:hover {
  background: #fef2f2;
}

.mobileLoginBtn {
  display: block;
  width: 100%;
  padding: 0.75rem;
  background: #d32f2f;
  color: white;
  text-decoration: none;
  font-weight: 600;
  text-align: center;
  border-radius: 8px;
  transition: background-color 0.2s;
}

.mobileLoginBtn:hover {
  background: #b71c1c;
}

.mobileOnly {
  display: none;
  width: 20%;
}

@media (max-width: 768px) {
  .header {
    padding: 0.5rem 0;
  }

  .container {
    padding: 0 1rem;
    position: relative;
  }

  /* Hide desktop navigation and auth section on mobile */
  .nav {
    display: none;
  }

  .authSection {
    display: none;
  }

  /* Show hamburger button on mobile */
  .hamburgerBtn {
    display: flex;
  }

  /* Show mobile menu overlay on mobile */
  .mobileMenuOverlay {
    display: block;
  }

  .logo h1 {
    font-size: 1.2rem;
    display: none;
  }

  /* Ensure logo and hamburger are properly spaced */
  .container {
    justify-content: space-between;
  }

  .mobileOnly {
    display: inline-block;
  }
  .mobileLoginBtn {
    width: 80%;
    font-size: 13px;
    background: #00a279;
    height: 40px;
  }

  .loginAndHamburgerMobile {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
}

@media (prefers-color-scheme: dark) {
  .header {
    background: #1a1a1a;
    border-bottom-color: #333;
  }

  .nav a {
    color: #e5e5e5;
  }

  .nav a:hover {
    color: #ff6b6b;
  }

  .userMenuTrigger {
    border-color: #333;
    color: #e5e5e5;
  }

  .userMenuTrigger:hover {
    background: #2a2a2a;
    border-color: #ff6b6b;
  }

  .userMenuTrigger:focus {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 2px rgba(255, 107, 107, 0.1);
  }

  .userInfo {
    color: #e5e5e5;
  }

  .dropdownIcon {
    color: #999;
  }

  .dropdownMenu {
    background: #2a2a2a;
    border-color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  .dropdownItem {
    color: #e5e5e5;
  }

  .dropdownItem:hover {
    background: #333;
  }

  .dropdownItem:focus {
    background: #333;
  }

  .logoutItem {
    color: #ff6b6b;
    border-top-color: #333;
  }

  .logoutItem:hover {
    background: #2d1b1b;
  }

  .profileLink {
    background: #66bb6a;
  }

  .profileLink:hover {
    background: #4caf50;
  }

  .loginBtn {
    background: #ff6b6b;
  }

  .loginBtn:hover {
    background: #ff5252;
  }

  .logoutBtn {
    color: #ff6b6b;
    border-color: #ff6b6b;
  }

  .logoutBtn:hover {
    background: #ff6b6b;
    color: white;
  }

  /* Dark mode hamburger menu styles */
  .hamburgerBtn:hover {
    background: #2a2a2a;
  }

  .hamburgerBtn:focus {
    background: #2a2a2a;
  }

  .hamburgerLine {
    background: #e5e5e5;
  }

  .mobileMenu {
    background: #2a2a2a;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
  }

  .mobileMenuHeader {
    background: #1a1a1a;
    border-bottom-color: #333;
  }

  .mobileMenuHeader h2 {
    color: #e5e5e5;
  }

  .mobileMenuClose {
    color: #999;
  }

  .mobileMenuClose:hover {
    background: #333;
  }

  .mobileNav li {
    border-bottom-color: #333;
  }

  .mobileNav a {
    color: #e5e5e5;
  }

  .mobileNav a:hover {
    background: #333;
    color: #ff6b6b;
  }

  .mobileAuthSection {
    border-top-color: #333;
  }

  .mobileUserInfo {
    color: #e5e5e5;
    background: #1a1a1a;
  }

  .mobileMenuItem {
    color: #e5e5e5;
  }

  .mobileMenuItem:hover {
    background: #333;
  }

  .mobileLogoutItem {
    color: #ff6b6b;
    border-top-color: #333;
  }

  .mobileLogoutItem:hover {
    background: #2d1b1b;
  }

  .mobileLoginBtn {
    background: #ff6b6b;
  }

  .mobileLoginBtn:hover {
    background: #ff5252;
  }
}
