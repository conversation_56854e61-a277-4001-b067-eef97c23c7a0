import React from 'react';

interface ErrorPopupProps {
  messages: string[];
  onClose: () => void;
}

const ErrorPopup: React.FC<ErrorPopupProps> = ({ messages, onClose }) => {
  return (
    <div style={{
      position: 'fixed',
      top: 0,
      left: 0,
      width: '100vw',
      height: '100vh',
      background: 'rgba(0,0,0,0.3)',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 9999,
    }}>
      <div style={{
        background: '#fff',
        borderRadius: 8,
        padding: 24,
        minWidth: 320,
        boxShadow: '0 2px 16px rgba(0,0,0,0.15)',
        textAlign: 'center',
      }}>
        <h3 style={{ color: '#d32f2f', marginBottom: 16 }}>خطا</h3>
        <ul style={{ color: '#d32f2f', marginBottom: 16, padding: 0, listStyle: 'none' }}>
          {messages.map((msg, idx) => (
            <li key={idx}>{msg}</li>
          ))}
        </ul>
        <button onClick={onClose} style={{
          background: '#d32f2f',
          color: '#fff',
          border: 'none',
          borderRadius: 4,
          padding: '8px 24px',
          cursor: 'pointer',
        }}>
          بستن
        </button>
      </div>
    </div>
  );
};

export default ErrorPopup;
