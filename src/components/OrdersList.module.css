.container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
}

.loading, .error {
  text-align: center;
  padding: 3rem 1rem;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.error {
  color: #d32f2f;
  border: 2px solid #ffcdd2;
}

.empty {
  text-align: center;
  padding: 4rem 2rem;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.empty h3 {
  color: #d32f2f;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.empty p {
  color: #666;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.createOrderBtn {
  display: inline-block;
  background: #d32f2f;
  color: white;
  text-decoration: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.createOrderBtn:hover {
  background: #b71c1c;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.ordersList {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.loadMore {
  text-align: center;
  margin-top: 2rem;
}

.loadMoreBtn {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.loadMoreBtn:hover:not(:disabled) {
  background: #b71c1c;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.loadMoreBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.endMessage {
  text-align: center;
  margin-top: 2rem;
  padding: 1rem;
  color: #666;
  font-style: italic;
}

@media (max-width: 768px) {
  .container {
    padding: 0 0.5rem;
  }

  .empty {
    padding: 2rem 1rem;
  }

  .empty h3 {
    font-size: 1.3rem;
  }

  .empty p {
    font-size: 1rem;
  }

  .createOrderBtn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }

  .loadMoreBtn {
    padding: 0.875rem 1.5rem;
    font-size: 0.9rem;
  }
}

@media (prefers-color-scheme: dark) {
  .loading, .error, .empty {
    background: #1a1a1a;
    color: #e5e5e5;
  }

  .error {
    color: #ff6b6b;
    border-color: #ff6b6b;
  }

  .empty h3 {
    color: #ff6b6b;
  }

  .empty p {
    color: #ccc;
  }

  .createOrderBtn {
    background: #ff6b6b;
  }

  .createOrderBtn:hover {
    background: #ff5252;
  }

  .loadMoreBtn {
    background: #ff6b6b;
  }

  .loadMoreBtn:hover:not(:disabled) {
    background: #ff5252;
  }

  .endMessage {
    color: #999;
  }
}
