"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { ProfileService } from "@/services/profileService"; // Import ProfileService
import styles from "./CurrencyExchange.module.css";
import { Currency } from "@/types/currency";
import {
  getCurrencyDisplayInfo,
  // calculateExchangeRate,
  fetchCurrencies,
  convertCurrency,
  getDefaultCurrencies,
  createSwapHandler,
  createDropdownToggleHandler,
  createCurrencySelectHandler,
  createAmountChangeHandler,
  getFilteredCurrencies,
  createClickOutsideHandler,
  flagMap,
} from "./currencyExchangeHelpers";

export default function CurrencyExchange() {
  const [fromAmount, setFromAmount] = useState("1,000,000");
  const [toAmount, setToAmount] = useState("0");
  const [fromCurrency, setFromCurrency] = useState("toman");
  const [toCurrency, setToCurrency] = useState("toman");
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [fromDropdownOpen, setFromDropdownOpen] = useState(false);
  const [toDropdownOpen, setToDropdownOpen] = useState(false);
  const { isAuthenticated, token } = useAuth(); // Get token from useAuth
  const router = useRouter();

  // Fetch currencies on component mount
  useEffect(() => {
    const loadCurrencies = async () => {
      try {
        setLoading(true);
        setError(null);
        const fetchedCurrencies = await fetchCurrencies();
        setCurrencies(fetchedCurrencies);

        // Set default currencies if available
        const defaultCurrencies = getDefaultCurrencies(fetchedCurrencies);
        setFromCurrency(defaultCurrencies.from);
        setToCurrency(defaultCurrencies.to);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "Failed to load currencies"
        );
        console.error("Error fetching currencies:", err);
      } finally {
        setLoading(false);
      }
    };

    loadCurrencies();
  }, []);

  // Event handlers using helper functions
  const swapCurrencies = createSwapHandler(
    fromCurrency,
    toCurrency,
    fromAmount,
    toAmount,
    setFromCurrency,
    setToCurrency,
    setFromAmount,
    setToAmount
  );

  const toggleFromDropdown = createDropdownToggleHandler(
    fromDropdownOpen,
    setFromDropdownOpen,
    setToDropdownOpen
  );

  const toggleToDropdown = createDropdownToggleHandler(
    toDropdownOpen,
    setToDropdownOpen,
    setFromDropdownOpen
  );

  const selectFromCurrency = createCurrencySelectHandler(
    setFromCurrency,
    setFromDropdownOpen
  );

  const selectToCurrency = createCurrencySelectHandler(
    setToCurrency,
    setToDropdownOpen
  );

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = createClickOutsideHandler(
      setFromDropdownOpen,
      setToDropdownOpen
    );
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const changeFromAmount = createAmountChangeHandler(
    fromCurrency,
    toCurrency,
    currencies,
    setFromAmount,
    setToAmount,
    false
  );

  const changeToAmount = createAmountChangeHandler(
    fromCurrency,
    toCurrency,
    currencies,
    setFromAmount,
    setToAmount,
    true
  );

  // Recalculate toAmount when fromCurrency or toCurrency changes
  useEffect(() => {
    const value = fromAmount.replace(/,/g, "");
    if (!isNaN(Number(value)) && currencies.length > 0) {
      const convertedAmount = convertCurrency(
        value,
        fromCurrency,
        toCurrency,
        currencies,
        toCurrency === "toman"
      );
      setToAmount(convertedAmount);
    }
  }, [fromCurrency, toCurrency, currencies, fromAmount]);

  // Handler for Start Transaction button
  const handleStartTransaction = async () => {
    // Make the function async
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    if (!token) {
      // This case should ideally not happen if isAuthenticated is true, but good for type safety
      router.push("/auth");
      return;
    }

    try {
      const userProfile = await ProfileService.getProfile(token);
      if (userProfile.data?.approvalStatus === "approved") {
        // Check if user has at least one bank card
        const { getBankCards } = await import("@/services/bankCardService");
        const cards = await getBankCards(token);
        if (cards && cards.length > 0) {
          // Pass exchange data to order page via URL parameters
          const fromCurrencyData = currencies.find(
            (c) => c.name === fromCurrency
          );
          const toCurrencyData = currencies.find((c) => c.name === toCurrency);

          const params = new URLSearchParams({
            fromCurrency,
            toCurrency,
            fromAmount: fromAmount.replace(/,/g, ""),
            toAmount: toAmount.replace(/,/g, ""),
            fromCurrencyFa: fromCurrencyData?.fa || fromCurrency,
            toCurrencyFa: toCurrencyData?.fa || toCurrency,
          });

          router.push(`/order?${params.toString()}`);
        } else {
          // No cards, redirect to bank-cards page
          router.push("/bank-cards");
        }
      } else {
        // Pass approvalStatus as a query parameter to the profile page
        router.push(
          `/profile?approvalStatus=${
            userProfile.data?.approvalStatus || "not_submitted"
          }`
        );
      }
    } catch (error) {
      console.error("Error fetching user profile or bank cards:", error);
      // Optionally, handle error by navigating to a generic error page or profile page
      router.push("/profile");
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: "center", padding: "2rem" }}>
            <p>در حال بارگذاری ارزها...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.exchangeCard}>
          <div style={{ textAlign: "center", padding: "2rem", color: "red" }}>
            <p>خطا در بارگذاری ارزها: {error}</p>
            <button
              onClick={() => window.location.reload()}
              style={{ marginTop: "1rem", padding: "0.5rem 1rem" }}
            >
              تلاش مجدد
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.exchangeCard}>
        {/* From Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(fromCurrency, currencies).flag}
              </div>
            </div>
            <button className={styles.dropdownBtn} onClick={toggleFromDropdown}>
              {fromDropdownOpen ? (
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ verticalAlign: "middle" }}
                >
                  <path
                    d="M7 14l5-5 5 5"
                    stroke="#555"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              ) : (
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ verticalAlign: "middle" }}
                >
                  <path
                    d="M7 10l5 5 5-5"
                    stroke="#555"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </button>
          </div>
          {fromDropdownOpen && (
            <div className={styles.dropdown}>
              {getFilteredCurrencies(currencies, fromCurrency, true).map(
                (currency) => (
                  <div
                    key={currency._id}
                    className={`${styles.dropdownItem} ${
                      currency.name === fromCurrency
                        ? styles.dropdownItemActive
                        : ""
                    }`}
                    onClick={() => selectFromCurrency(currency.name)}
                  >
                    <div className={styles.dropdownItemInfo}>
                      <span className={styles.dropdownItemFlag}>
                         {currency.name.toLowerCase() === "usdt" ? (
                          <img
                            src="/tether.png"
                            alt="USDT"
                            width="24"
                            height="24"
                          />
                        ) : (
                          flagMap[currency.name.toLowerCase()] || "💱"
                        )}
                      </span>
                      <span className={styles.dropdownItemName}>
                        {currency.fa} ({currency.name.toUpperCase()})
                      </span>
                    </div>
                  </div>
                )
              )}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={fromAmount}
              onChange={changeFromAmount}
              className={`${styles.amountInput} ltr-input`}
            />
            <span className={`${styles.amountLabel} fa-text`}>
              پرداخت می کنید
            </span>
          </div>
        </div>

        {/* Swap Button */}
        <div className={styles.swapSection}>
          <button className={styles.swapBtn} onClick={swapCurrencies}>
            ⇅
          </button>
        </div>

        {/* To Currency */}
        <div className={`${styles.currencySection} dropdown-container`}>
          <div className={styles.currencyHeader}>
            <div className={styles.currencyInfo}>
              <span className={styles.currencyName}>
                {getCurrencyDisplayInfo(toCurrency, currencies).name}
              </span>
              <div className={styles.flag}>
                {getCurrencyDisplayInfo(toCurrency, currencies).flag}
              </div>
            </div>
            <button className={styles.dropdownBtn} onClick={toggleToDropdown}>
              {toDropdownOpen ? (
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ verticalAlign: "middle" }}
                >
                  <path
                    d="M7 14l5-5 5 5"
                    stroke="#555"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              ) : (
                <svg
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  style={{ verticalAlign: "middle" }}
                >
                  <path
                    d="M7 10l5 5 5-5"
                    stroke="#555"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )}
            </button>
          </div>
          {toDropdownOpen && (
            <div className={styles.dropdown}>
              {getFilteredCurrencies(currencies, fromCurrency, false).map(
                (currency) => (
                  <div
                    key={currency._id}
                    className={`${styles.dropdownItem} ${
                      currency.name === toCurrency
                        ? styles.dropdownItemActive
                        : ""
                    }`}
                    onClick={() => selectToCurrency(currency.name)}
                  >
                    <div className={styles.dropdownItemInfo}>
                      <span className={styles.dropdownItemFlag}>
                        {currency.name.toLowerCase() === "usdt" ? (
                          <img
                            src="/tether.png"
                            alt="USDT"
                            width="24"
                            height="24"
                          />
                        ) : (
                          flagMap[currency.name.toLowerCase()] || "💱"
                        )}
                      </span>
                      <span className={styles.dropdownItemName}>
                        {currency.fa} ({currency.name.toUpperCase()})
                      </span>
                    </div>
                  </div>
                )
              )}
            </div>
          )}
          <div className={styles.amountSection}>
            <input
              type="text"
              value={toAmount}
              onChange={changeToAmount}
              className={`${styles.amountInput} ltr-input`}
            />
            <span className={`${styles.amountLabel} fa-text`}>
              دریافت می کنید
            </span>
          </div>
        </div>

        {/* Exchange Rate */}
        {/* <div className={styles.exchangeRate + ' fa-text'}>
          {(() => {
            const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
            const toCurrencyData = currencies.find(c => c.name === toCurrency);

            if (fromCurrencyData && toCurrencyData) {
              const rate = calculateExchangeRate(fromCurrency, toCurrency, currencies);
              if (rate !== null) {
                return `1 ${fromCurrencyData.fa} = ${rate.toFixed(4)} ${toCurrencyData.fa}`;
              }
            }

            return 'نرخ تبدیل در دسترس نیست';
          })()}
        </div> */}

        {/* Start Transaction Button */}
        <button
          className={styles.startBtn + " fa-text"}
          onClick={handleStartTransaction}
        >
          ثبت سفارش
        </button>
      </div>
    </div>
  );
}
