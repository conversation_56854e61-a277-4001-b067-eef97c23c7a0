.orderCard {
  background: #fff;
  border: 2px solid #e5e5e5;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.orderCard:hover {
  border-color: #d32f2f;
  box-shadow: 0 6px 20px rgba(211, 47, 47, 0.15);
}

.orderHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.orderInfo {
  flex: 1;
}

.orderTitle {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.orderNumber {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.orderStatus {
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  text-align: center;
}

.orderDate {
  color: #666;
  font-size: 0.9rem;
}

.toggleButton {
  background: transparent;
  border: 1px solid #e5e5e5;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
  font-size: 1.2rem;
}

.toggleButton:hover {
  background: #f5f5f5;
  border-color: #d32f2f;
  color: #d32f2f;
}

.orderSummary {
  margin-bottom: 1rem;
}

.exchangeInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 10px;
}

.exchangeItem {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.label {
  font-size: 0.8rem;
  color: #666;
  margin-bottom: 0.25rem;
}

.value {
  font-weight: 600;
  color: #333;
  font-size: 1.1rem;
}

.exchangeArrow {
  font-size: 1.5rem;
  color: #d32f2f;
  font-weight: bold;
}

.orderDetails {
  border-top: 1px solid #e5e5e5;
  padding-top: 1rem;
  margin-top: 1rem;
}

.detailsGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.detailItem {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.detailLabel {
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.detailValue {
  font-weight: 600;
  color: #333;
}

@media (max-width: 768px) {
  .orderCard {
    padding: 1rem;
  }

  .orderTitle {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .orderNumber {
    font-size: 1rem;
  }

  .exchangeInfo {
    flex-direction: column;
    gap: 0.5rem;
    text-align: center;
  }

  .exchangeArrow {
    transform: rotate(90deg);
    font-size: 1.2rem;
  }

  .detailsGrid {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }

  .toggleButton {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
}

@media (prefers-color-scheme: dark) {
  .orderCard {
    background: #1a1a1a;
    border-color: #333;
    color: #e5e5e5;
  }

  .orderCard:hover {
    border-color: #ff6b6b;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.15);
  }

  .orderNumber {
    color: #e5e5e5;
  }

  .orderDate {
    color: #999;
  }

  .toggleButton {
    border-color: #333;
    color: #999;
  }

  .toggleButton:hover {
    background: #2a2a2a;
    border-color: #ff6b6b;
    color: #ff6b6b;
  }

  .exchangeInfo {
    background: #2a2a2a;
  }

  .label {
    color: #999;
  }

  .value {
    color: #e5e5e5;
  }

  .exchangeArrow {
    color: #ff6b6b;
  }

  .orderDetails {
    border-top-color: #333;
  }

  .detailLabel {
    color: #999;
  }

  .detailValue {
    color: #e5e5e5;
  }
}
