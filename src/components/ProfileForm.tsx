'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { ProfileService } from '@/services/profileService';
import { UserProfile, UpdateProfileRequest } from '@/types/auth';
import {
  validateName,
  validateFamilyName,
  validateNationalCode,
  validateState,
  validateCity,
  validateAddress,
  validateImageFile
} from '@/utils/validation';
import styles from './ProfileForm.module.css';

interface ProfileFormErrors {
  name?: string;
  familyName?: string;
  nationalCode?: string;
  state?: string;
  city?: string;
  address?: string;
  nationalCardImagePath?: string;
  authImagePath?: string;
}

export default function ProfileForm() {
  const { token, user } = useAuth();
  const [formData, setFormData] = useState<Partial<UserProfile>>({
    name: '',
    familyName: '',
    nationalCode: '',
    state: '',
    city: '',
    address: '',
    nationalCardImagePath: '',
    authImagePath: '',
  });

  const [errors, setErrors] = useState<ProfileFormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [nationalCardFile, setNationalCardFile] = useState<File | null>(null);
  const [authImageFile, setAuthImageFile] = useState<File | null>(null);

  // Load existing profile data
  useEffect(() => {
    const loadProfile = async () => {
      if (!token || !user?.id) return;

      setIsLoading(true);
      try {
        const response = await ProfileService.getProfile(token);
        if (response.success && response.data) {
          setFormData(response.data);
        }
      } catch (error) {
        console.error('Error loading profile:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadProfile();
  }, [token, user]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof ProfileFormErrors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }

    // Clear success message when user makes changes
    if (successMessage) {
      setSuccessMessage('');
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>, fileType: 'nationalCard' | 'authImage') => {
    const file = e.target.files?.[0] || null;
    
    if (fileType === 'nationalCard') {
      setNationalCardFile(file);
      // Clear error
      if (errors.nationalCardImagePath) {
        setErrors(prev => ({ ...prev, nationalCardImagePath: undefined }));
      }
    } else {
      setAuthImageFile(file);
      // Clear error
      if (errors.authImagePath) {
        setErrors(prev => ({ ...prev, authImagePath: undefined }));
      }
    }

    // Clear success message when user makes changes
    if (successMessage) {
      setSuccessMessage('');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: ProfileFormErrors = {};

    // Validate text fields
    const nameError = validateName(formData.name || '');
    if (nameError) newErrors.name = nameError;

    const familyNameError = validateFamilyName(formData.familyName || '');
    if (familyNameError) newErrors.familyName = familyNameError;

    if (formData.nationalCode !== undefined) {
      const nationalCodeError = validateNationalCode(formData.nationalCode);
      if (nationalCodeError) newErrors.nationalCode = nationalCodeError;
    }

    if (formData.state !== undefined) {
      const stateError = validateState(formData.state);
      if (stateError) newErrors.state = stateError;
    }

    if (formData.city !== undefined) {
      const cityError = validateCity(formData.city);
      if (cityError) newErrors.city = cityError;
    }

    if (formData.address !== undefined) {
      const addressError = validateAddress(formData.address);
      if (addressError) newErrors.address = addressError;
    }

    // Validate image files (only if not already uploaded)
    if (!formData.nationalCardImagePath && !nationalCardFile) {
      const nationalCardError = validateImageFile(null);
      if (nationalCardError) newErrors.nationalCardImagePath = nationalCardError;
    } else if (nationalCardFile) {
      const nationalCardError = validateImageFile(nationalCardFile);
      if (nationalCardError) newErrors.nationalCardImagePath = nationalCardError;
    }

    if (!formData.authImagePath && !authImageFile) {
      const authImageError = validateImageFile(null);
      if (authImageError) newErrors.authImagePath = authImageError;
    } else if (authImageFile) {
      const authImageError = validateImageFile(authImageFile);
      if (authImageError) newErrors.authImagePath = authImageError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || !token) {
      return;
    }

    setIsSubmitting(true);
    
    try {
      const filteredFormData: UpdateProfileRequest = {
        name: formData.name,
        familyName: formData.familyName,
        nationalCode: formData.nationalCode,
        state: formData.state,
        city: formData.city,
        address: formData.address,
        nationalCardImage: nationalCardFile || formData.nationalCardImagePath,
        authImage: authImageFile || formData.authImagePath,
      };

      // Filter out undefined fields explicitly
      const finalUpdateData: UpdateProfileRequest = {};

      if (filteredFormData.name !== undefined) finalUpdateData.name = filteredFormData.name;
      if (filteredFormData.familyName !== undefined) finalUpdateData.familyName = filteredFormData.familyName;
      if (filteredFormData.nationalCode !== undefined) finalUpdateData.nationalCode = filteredFormData.nationalCode;
      if (filteredFormData.state !== undefined) finalUpdateData.state = filteredFormData.state;
      if (filteredFormData.city !== undefined) finalUpdateData.city = filteredFormData.city;
      if (filteredFormData.address !== undefined) finalUpdateData.address = filteredFormData.address;
      if (filteredFormData.nationalCardImage !== undefined) finalUpdateData.nationalCardImage = filteredFormData.nationalCardImage;
      if (filteredFormData.authImage !== undefined) finalUpdateData.authImage = filteredFormData.authImage;

      const response = await ProfileService.updateProfile(finalUpdateData, token);
      
      if (response.success) {
        setSuccessMessage('اطلاعات پروفایل با موفقیت به‌روزرسانی شد');
        // Update form data with response data if available
        if (response.data) {
          setFormData(response.data);
        }
        // Clear file inputs
        setNationalCardFile(null);
        setAuthImageFile(null);
      } else {
        throw new Error(response.message || 'خطا در به‌روزرسانی پروفایل');
      }
      
    } catch (error) {
      console.error('Error updating profile:', error);
      setErrors({ 
        name: error instanceof Error ? error.message : 'خطا در به‌روزرسانی پروفایل. لطفاً دوباره تلاش کنید.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <div className={styles.loading}>در حال بارگذاری...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <h2 className={styles.formTitle}>اطلاعات پروفایل</h2>
        
        {successMessage && (
          <div className={styles.successMessage}>{successMessage}</div>
        )}
        
        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.row}>
            <div className={styles.formGroup}>
              <label htmlFor="name" className={styles.label}>
                نام *
              </label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name ?? ''}
                onChange={handleInputChange}
                className={`${styles.input} ${errors.name ? styles.inputError : ''}`}
                placeholder="نام خود را وارد کنید"
                disabled={isSubmitting}
              />
              {errors.name && (
                <span className={styles.errorMessage}>{errors.name}</span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="familyName" className={styles.label}>
                نام خانوادگی *
              </label>
              <input
                type="text"
                id="familyName"
                name="familyName"
                value={formData.familyName ?? ''}
                onChange={handleInputChange}
                className={`${styles.input} ${errors.familyName ? styles.inputError : ''}`}
                placeholder="نام خانوادگی خود را وارد کنید"
                disabled={isSubmitting}
              />
              {errors.familyName && (
                <span className={styles.errorMessage}>{errors.familyName}</span>
              )}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="nationalCode" className={styles.label}>
              کد ملی
            </label>
            <input
              type="text"
              id="nationalCode"
              name="nationalCode"
              value={formData.nationalCode || ''}
              onChange={handleInputChange}
              className={`${styles.input} ${errors.nationalCode ? styles.inputError : ''}`}
              placeholder="کد ملی خود را وارد کنید (اختیاری)"
              disabled={isSubmitting}
              maxLength={10}
            />
            {errors.nationalCode && (
              <span className={styles.errorMessage}>{errors.nationalCode}</span>
            )}
          </div>

          <div className={styles.row}>
            <div className={styles.formGroup}>
              <label htmlFor="state" className={styles.label}>
                استان *
              </label>
              <input
                type="text"
                id="state"
                name="state"
                value={formData.state ?? ''}
                onChange={handleInputChange}
                className={`${styles.input} ${errors.state ? styles.inputError : ''}`}
                placeholder="استان خود را وارد کنید"
                disabled={isSubmitting}
              />
              {errors.state && (
                <span className={styles.errorMessage}>{errors.state}</span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="city" className={styles.label}>
                شهر *
              </label>
              <input
                type="text"
                id="city"
                name="city"
                value={formData.city ?? ''}
                onChange={handleInputChange}
                className={`${styles.input} ${errors.city ? styles.inputError : ''}`}
                placeholder="شهر خود را وارد کنید"
                disabled={isSubmitting}
              />
              {errors.city && (
                <span className={styles.errorMessage}>{errors.city}</span>
              )}
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="address" className={styles.label}>
              آدرس *
            </label>
            <textarea
              id="address"
              name="address"
              value={formData.address ?? ''}
              onChange={handleInputChange}
              className={`${styles.textarea} ${errors.address ? styles.inputError : ''}`}
              placeholder="آدرس کامل خود را وارد کنید"
              disabled={isSubmitting}
              rows={3}
            />
            {errors.address && (
              <span className={styles.errorMessage}>{errors.address}</span>
            )}
          </div>

          <div className={styles.row}>
            <div className={styles.formGroup}>
              <label htmlFor="nationalCardImage" className={styles.label}>
                تصویر کارت ملی *
              </label>
              <input
                type="file"
                id="nationalCardImage"
                accept="image/jpeg,image/jpg,image/png"
                onChange={(e) => handleFileChange(e, 'nationalCard')}
                className={`${styles.fileInput} ${errors.nationalCardImagePath ? styles.inputError : ''}`}
                disabled={isSubmitting}
              />
              {formData.nationalCardImagePath && !nationalCardFile && (
                <div className={styles.currentImage}>
                  تصویر فعلی: موجود ✓
                </div>
              )}
              {nationalCardFile && (
                <div className={styles.selectedFile}>
                  فایل انتخاب شده: {nationalCardFile.name}
                </div>
              )}
              {errors.nationalCardImagePath && (
                <span className={styles.errorMessage}>{errors.nationalCardImagePath}</span>
              )}
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="authImage" className={styles.label}>
                تصویر احراز هویت *
              </label>
              <div>
              روی یک برگه نام نام خانوادگی و کد ملی خود را بنویسید و در کنار صورت خود عکس بگیرید.
              </div>
              <input
                type="file"
                id="authImage"
                accept="image/jpeg,image/jpg,image/png"
                onChange={(e) => handleFileChange(e, 'authImage')}
                className={`${styles.fileInput} ${errors.authImagePath ? styles.inputError : ''}`}
                disabled={isSubmitting}
              />
              {formData.authImagePath && !authImageFile && (
                <div className={styles.currentImage}>
                  تصویر فعلی: موجود ✓
                </div>
              )}
              {authImageFile && (
                <div className={styles.selectedFile}>
                  فایل انتخاب شده: {authImageFile.name}
                </div>
              )}
              {errors.authImagePath && (
                <span className={styles.errorMessage}>{errors.authImagePath}</span>
              )}
            </div>
          </div>

          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'در حال به‌روزرسانی...' : 'به‌روزرسانی پروفایل'}
          </button>
        </form>
      </div>
    </div>
  );
}
