.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f9fa; /* Light gray background */
  color: #343a40; /* Dark text color */
}

.mainContent {
  flex: 1;
  padding: 40px 20px;
  max-width: 900px;
  margin: 0 auto;
  background-color: #ffffff; /* White content background */
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  line-height: 1.8;
}

.title {
  font-size: 2.8rem;
  color: #007bff; /* Primary blue color */
  text-align: center;
  margin-bottom: 30px;
  font-weight: bold;
  position: relative;
}

.title::after {
  content: '';
  display: block;
  width: 80px;
  height: 4px;
  background-color: #007bff;
  margin: 15px auto 0;
  border-radius: 2px;
}

.description {
  font-size: 1.1rem;
  margin-bottom: 30px;
  text-align: justify;
}

.contactInfo {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.infoItem {
  background-color: #eaf6ff; /* Light blue for info items */
  padding: 20px;
  border-radius: 8px;
  border-left: 5px solid #007bff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

.infoItem h2 {
  font-size: 1.6rem;
  color: #0056b3;
  margin-top: 0;
  margin-bottom: 15px;
}

.infoItem p {
  font-size: 1.05rem;
  margin-bottom: 5px;
}

.contactForm {
  background-color: #f0f8ff; /* Lighter blue for form background */
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

.formGroup {
  margin-bottom: 20px;
}

.formGroup label {
  display: block;
  font-size: 1.1rem;
  color: #343a40;
  margin-bottom: 8px;
  font-weight: 600;
}

.formGroup input[type="text"],
.formGroup input[type="email"],
.formGroup textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ced4da;
  border-radius: 5px;
  font-size: 1rem;
  box-sizing: border-box; /* Include padding in width */
  transition: border-color 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.formGroup input[type="text"]:focus,
.formGroup input[type="email"]:focus,
.formGroup textarea:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  outline: none;
}

.formGroup textarea {
  resize: vertical; /* Allow vertical resizing */
  min-height: 120px;
}

.submitButton {
  background-color: #007bff;
  color: white;
  padding: 12px 25px;
  border: none;
  border-radius: 5px;
  font-size: 1.1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: auto;
  display: block;
  margin-top: 20px;
}

.submitButton:hover {
  background-color: #0056b3;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .mainContent {
    padding: 30px 15px;
    margin: 20px;
  }

  .title {
    font-size: 2.2rem;
  }

  .description {
    font-size: 1rem;
  }

  .contactInfo {
    grid-template-columns: 1fr; /* Stack items on smaller screens */
  }

  .contactForm {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .mainContent {
    padding: 20px 10px;
    margin: 10px;
  }

  .title {
    font-size: 1.8rem;
  }

  .submitButton {
    width: 100%;
  }
}
