"use client";

import { Order } from "@/types/order";
import { Currency } from "@/types/currency";
import OrderCard from "./OrderCard";
import styles from "./OrdersList.module.css";

interface OrdersListProps {
  orders: Order[];
  currencies: Currency[];
  loading: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => void;
  getCurrencyDisplayName: (currencyName: string) => string;
  page: number;
}

export default function OrdersList({
  orders,
  currencies,
  loading,
  error,
  hasMore,
  loadMore,
  getCurrencyDisplayName,
  page,
}: OrdersListProps) {
  if (loading && page === 1) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <p>در حال بارگذاری سفارشات...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.error}>
          <p>{error}</p>
        </div>
      </div>
    );
  }

  if (orders.length === 0) {
    return (
      <div className={styles.container}>
        <div className={styles.empty}>
          <h3>هیچ سفارشی یافت نشد</h3>
          <p>شما هنوز هیچ سفارش تبدیل ارزی ثبت نکرده‌اید.</p>
          <a href="/" className={styles.createOrderBtn}>
            ثبت سفارش جدید
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.ordersList}>
        {orders && orders.map((order) => (
            <OrderCard
              key={order._id}
              order={order}
              getCurrencyDisplayName={getCurrencyDisplayName}
            />
          ))}
      </div>

      {hasMore && (
        <div className={styles.loadMore}>
          <button
            onClick={loadMore}
            disabled={loading}
            className={styles.loadMoreBtn}
          >
            {loading ? "در حال بارگذاری..." : "نمایش بیشتر"}
          </button>
        </div>
      )}

      {!hasMore && orders.length > 0 && (
        <div className={styles.endMessage}>
          <p>همه سفارشات نمایش داده شد</p>
        </div>
      )}
    </div>
  );
}
