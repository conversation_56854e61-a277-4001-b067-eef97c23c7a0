.container {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  min-height: 60vh;
  padding: 2rem 1rem;
}

.formCard {
  background: #fff;
  border: 2px solid #d32f2f;
  border-radius: 20px;
  padding: 2rem;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 10px 30px rgba(211, 47, 47, 0.1);
}

.formTitle {
  color: #d32f2f;
  font-size: 1.8rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formRow {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.formGroup.fullWidth {
  grid-column: 1 / -1;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.input {
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f9f9f9;
}

.input:focus {
  outline: none;
  border-color: #d32f2f;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.cardNumberInput {
  font-family: 'Courier New', monospace;
  letter-spacing: 1px;
}

.ibanInput {
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

.actions {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: flex-end;
}

.submitButton,
.cancelButton {
  padding: 1rem 2rem;
  border: none;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.submitButton {
  background: #d32f2f;
  color: white;
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.2);
}

.submitButton:hover:not(:disabled) {
  background: #b71c1c;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(211, 47, 47, 0.3);
}

.submitButton:active:not(:disabled) {
  transform: translateY(0);
}

.submitButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.cancelButton {
  background: #f5f5f5;
  color: #666;
  border: 2px solid #e0e0e0;
}

.cancelButton:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.inputHint {
  font-size: 0.85rem;
  color: #666;
  margin-top: 0.25rem;
}

.bankSelect {
  padding: 1rem;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  font-size: 1rem;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bankSelect:focus {
  outline: none;
  border-color: #d32f2f;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .formCard {
    padding: 1.5rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .formRow {
    grid-template-columns: 1fr;
  }

  .actions {
    flex-direction: column;
  }

  .submitButton,
  .cancelButton {
    width: 100%;
  }
}

@media (prefers-color-scheme: dark) {
  .formCard {
    background: #1a1a1a;
    border-color: #ff6b6b;
    color: #e5e5e5;
  }

  .formTitle {
    color: #ff6b6b;
  }

  .label {
    color: #e5e5e5;
  }

  .input,
  .bankSelect {
    background: #2c2c2c;
    border-color: #444;
    color: #e5e5e5;
  }

  .input:focus,
  .bankSelect:focus {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }

  .input:disabled {
    background-color: #333;
  }

  .submitButton {
    background: #ff6b6b;
  }

  .submitButton:hover:not(:disabled) {
    background: #ff5252;
  }

  .cancelButton {
    background: #333;
    color: #e5e5e5;
    border-color: #555;
  }

  .cancelButton:hover {
    background: #444;
  }

  .inputHint {
    color: #ccc;
  }
}
