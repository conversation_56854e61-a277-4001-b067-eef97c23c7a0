.footer {
  background: #2c2c2c;
  color: #e5e5e5;
  padding: 2rem 0 0;
  margin-top: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
}

.section h3 {
  color: #0ac5b2;
  margin-bottom: 1rem;
  font-size: 1.2rem;
}

.section h4 {
  color: #fff;
  margin-bottom: 0.8rem;
  font-size: 1rem;
}

.section p {
  line-height: 1.6;
  color: #ccc;
}

.section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.section li {
  margin-bottom: 0.5rem;
}

.section a {
  color: #ccc;
  text-decoration: none;
  transition: color 0.2s;
}

.section a:hover {
  color: #d32f2f;
}

.bottom {
  background: #1a1a1a;
  padding: 1rem 0;
  margin-top: 2rem;
  border-top: 1px solid #444;
}

.bottom p {
  text-align: center;
  margin: 0;
  color: #999;
  font-size: 0.9rem;
}

@media (max-width: 768px) {
  .container {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .section {
    text-align: center;
  }
}

@media (prefers-color-scheme: dark) {
  .footer {
    background: #0a0a0a;
  }
  
  .bottom {
    background: #000;
  }
}
