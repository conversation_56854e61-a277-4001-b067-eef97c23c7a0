.container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 60vh;
  padding: 2rem 1rem;
  gap: 2rem;
}

.formCard {
  background: #fff;
  border: 2px solid #d32f2f;
  border-radius: 20px;
  padding: 2rem;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(211, 47, 47, 0.1);
}

.formTitle {
  color: #d32f2f;
  font-size: 1.8rem;
  font-weight: bold;
  text-align: center;
  margin-bottom: 2rem;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.formGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 1rem;
}

.input {
  padding: 1rem;
  border: 2px solid #e5e5e5;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f9f9f9;
  direction: rtl;
  text-align: right;
}

.input:focus {
  outline: none;
  border-color: #d32f2f;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.input:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.inputError {
  border-color: #f44336;
  background: #fff5f5;
}

.inputError:focus {
  border-color: #f44336;
  box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.1);
}

.errorMessage {
  color: #f44336;
  font-size: 0.875rem;
  margin-top: 0.25rem;
}

.submitButton {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 10px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 1rem;
}

.submitButton:hover:not(:disabled) {
  background: #b71c1c;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.submitButton:active:not(:disabled) {
  transform: translateY(0);
}

.submitButton:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Exchange Table Styles */
.exchangeTable {
  margin-bottom: 1rem;
}

.table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
}

.tableLabel {
  font-weight: 600;
  color: #333;
  padding: 0.75rem;
  border-bottom: 1px solid #e5e5e5;
  text-align: right;
  width: 40%;
}

.tableValue {
  padding: 0.75rem;
  border-bottom: 1px solid #e5e5e5;
  text-align: right;
  font-weight: 500;
}

/* Card Selection Styles */
.select {
  padding: 1rem;
  border: 2px solid #e5e5e5;
  border-radius: 10px;
  font-size: 1rem;
  transition: all 0.2s ease;
  background: #f9f9f9;
  direction: rtl;
  text-align: right;
  cursor: pointer;
}

.select:focus {
  outline: none;
  border-color: #d32f2f;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.select:disabled {
  background: #f5f5f5;
  color: #999;
  cursor: not-allowed;
}

.singleCard {
  padding: 1rem;
  background: #f0f8ff;
  border: 2px solid #d32f2f;
  border-radius: 10px;
  text-align: center;
}

.cardInfo {
  margin-top: 0.5rem;
  font-size: 1.1rem;
  color: #d32f2f;
}

.noCards {
  padding: 1rem;
  background: #fff3cd;
  border: 2px solid #ffc107;
  border-radius: 10px;
  text-align: center;
  color: #856404;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
    gap: 1rem;
  }

  .formCard {
    padding: 1.5rem;
  }

  .formTitle {
    font-size: 1.5rem;
  }

  .input, .select {
    padding: 0.875rem;
  }

  .submitButton {
    padding: 0.875rem 1.5rem;
    font-size: 1rem;
  }

  .tableLabel, .tableValue {
    padding: 0.5rem;
    font-size: 0.9rem;
  }

  .singleCard, .noCards {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
}

@media (prefers-color-scheme: dark) {
  .formCard {
    background: #1a1a1a;
    border-color: #ff6b6b;
    color: #e5e5e5;
  }
  
  .formTitle {
    color: #ff6b6b;
  }
  
  .label {
    color: #e5e5e5;
  }
  
  .input {
    background: #2c2c2c;
    border-color: #444;
    color: #e5e5e5;
  }
  
  .input:focus {
    border-color: #ff6b6b;
    background: #333;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }
  
  .input:disabled {
    background: #1a1a1a;
    color: #666;
  }
  
  .inputError {
    border-color: #ff5252;
    background: #2a1a1a;
  }
  
  .inputError:focus {
    border-color: #ff5252;
    box-shadow: 0 0 0 3px rgba(255, 82, 82, 0.1);
  }
  
  .errorMessage {
    color: #ff5252;
  }
  
  .submitButton {
    background: #ff6b6b;
  }
  
  .submitButton:hover:not(:disabled) {
    background: #ff5252;
  }

  .tableLabel {
    color: #e5e5e5;
    border-bottom-color: #444;
  }

  .tableValue {
    border-bottom-color: #444;
  }

  .select {
    background: #2c2c2c;
    border-color: #444;
    color: #e5e5e5;
  }

  .select:focus {
    border-color: #ff6b6b;
    background: #333;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }

  .select:disabled {
    background: #1a1a1a;
    color: #666;
  }

  .singleCard {
    background: #1a2332;
    border-color: #ff6b6b;
    color: #e5e5e5;
  }

  .cardInfo {
    color: #ff6b6b;
  }

  .noCards {
    background: #2a2419;
    border-color: #ffc107;
    color: #ffc107;
  }
}
