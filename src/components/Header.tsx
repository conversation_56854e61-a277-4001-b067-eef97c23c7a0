'use client';

import { useAuth } from '@/contexts/AuthContext';
import { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import styles from './Header.module.css';

export default function Header() {
  const { isAuthenticated, user, logout, isLoading } = useAuth();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const mobileMenuRef = useRef<HTMLDivElement>(null);

  const handleLogout = () => {
    logout();
    setIsDropdownOpen(false);
    setIsMobileMenuOpen(false);
  };

  const toggleDropdown = () => {
    setIsDropdownOpen(!isDropdownOpen);
  };

  const closeDropdown = () => {
    setIsDropdownOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  const closeMobileMenu = () => {
    setIsMobileMenuOpen(false);
  };

  // Close dropdown and mobile menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        closeDropdown();
      }
      if (mobileMenuRef.current && !mobileMenuRef.current.contains(event.target as Node)) {
        closeMobileMenu();
      }
    };

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        closeDropdown();
        closeMobileMenu();
      }
    };

    if (isDropdownOpen || isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
    };
  }, [isDropdownOpen, isMobileMenuOpen]);

  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <a href="/">
          <div className={styles.logo} style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
            <img src="/logo.png" alt="Logo" width={60} height={50} />
            <h1 style={{ fontSize: '1.2rem', color: 'rgb(10, 172, 59)' }}>صرافی تومانکس</h1>
          </div>
        </a>

        {/* Desktop Navigation */}
        <nav className={styles.nav}>
          <ul>
            <li><Link href="/">خانه</Link></li>
            <li><Link href="/about-us">درباره ما</Link></li>
            <li><Link href="/contact-us">تماس با ما</Link></li>
          </ul>
        </nav>
        <div className={styles.loginAndHamburgerMobile}>
          {!isLoading && !isAuthenticated && (
            <a href="/auth" className={styles.mobileLoginBtn + ' ' + styles.mobileOnly}>
              ورود/ثبت نام
            </a>
          )}

          {/* Hamburger Menu Button */}
          <button
            className={styles.hamburgerBtn}
            onClick={toggleMobileMenu}
            aria-expanded={isMobileMenuOpen}
            aria-label="منوی اصلی"
          >
            <span className={`${styles.hamburgerLine} ${isMobileMenuOpen ? styles.hamburgerLineOpen1 : ''}`}></span>
            <span className={`${styles.hamburgerLine} ${isMobileMenuOpen ? styles.hamburgerLineOpen2 : ''}`}></span>
            <span className={`${styles.hamburgerLine} ${isMobileMenuOpen ? styles.hamburgerLineOpen3 : ''}`}></span>
          </button>
        </div>



        {/* Desktop Auth Section */}
        <div className={styles.authSection}>
          {!isLoading && (
            <>
              {isAuthenticated ? (
                <div className={styles.userMenu} ref={dropdownRef}>
                  <button
                    onClick={toggleDropdown}
                    className={styles.userMenuTrigger}
                    aria-expanded={isDropdownOpen}
                    aria-haspopup="true"
                  >
                    <span className={styles.userInfo}>
                      {user?.mobileNumber}
                    </span>
                    <svg
                      className={`${styles.dropdownIcon} ${isDropdownOpen ? styles.dropdownIconOpen : ''}`}
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                    >
                      <polyline points="6,9 12,15 18,9"></polyline>
                    </svg>
                  </button>

                  {isDropdownOpen && (
                    <div className={styles.dropdownMenu}>
                      <a
                        href="/profile"
                        className={styles.dropdownItem}
                        onClick={closeDropdown}
                      >
                        پروفایل
                      </a>
                      <a
                        href="/orders"
                        className={styles.dropdownItem}
                        onClick={closeDropdown}
                      >
                        سفارشات
                      </a>
                      <a
                        href="/bank-cards"
                        className={styles.dropdownItem}
                        onClick={closeDropdown}
                      >
                        کارت‌های بانکی
                      </a>
                      <button
                        onClick={handleLogout}
                        className={`${styles.dropdownItem} ${styles.logoutItem}`}
                      >
                        خروج
                      </button>
                    </div>
                  )}
                </div>
              ) : (
                // <a href="/login" className={styles.loginBtn}>
                <a href="/auth" className={styles.loginBtn}>
                  ورود
                </a>
              )}
            </>
          )}
        </div>

        {/* Mobile Menu Overlay */}
        {isMobileMenuOpen && (
          <div className={styles.mobileMenuOverlay} onClick={closeMobileMenu}>
            <div
              className={styles.mobileMenu}
              ref={mobileMenuRef}
              onClick={(e) => e.stopPropagation()}
            >
              <div className={styles.mobileMenuHeader}>
                <h2>منو</h2>
                <button
                  className={styles.mobileMenuClose}
                  onClick={closeMobileMenu}
                  aria-label="بستن منو"
                >
                  ×
                </button>
              </div>

              <nav className={styles.mobileNav}>
                <ul>
                  <li>
                    <Link href="/" onClick={closeMobileMenu}>
                      خانه
                    </Link>
                  </li>
                  <li>
                    <Link href="/about-us" onClick={closeMobileMenu}>
                      درباره ما
                    </Link>
                  </li>
                  <li>
                    <Link href="/contact-us" onClick={closeMobileMenu}>
                      تماس با ما
                    </Link>
                  </li>
                </ul>
              </nav>

              <div className={styles.mobileAuthSection}>
                {!isLoading && (
                  <>
                    {isAuthenticated ? (
                      <div className={styles.mobileUserMenu}>
                        <div className={styles.mobileUserInfo}>
                          {user?.mobileNumber}
                        </div>
                        <div className={styles.mobileUserActions}>
                          <a
                            href="/profile"
                            className={styles.mobileMenuItem}
                            onClick={closeMobileMenu}
                          >
                            پروفایل
                          </a>
                          <a
                            href="/orders"
                            className={styles.mobileMenuItem}
                            onClick={closeMobileMenu}
                          >
                            سفارشات
                          </a>
                          <a
                            href="/bank-cards"
                            className={styles.mobileMenuItem}
                            onClick={closeMobileMenu}
                          >
                            کارت‌های بانکی
                          </a>
                          <button
                            onClick={handleLogout}
                            className={`${styles.mobileMenuItem} ${styles.mobileLogoutItem}`}
                          >
                            خروج
                          </button>
                        </div>
                      </div>
                    ) : (
                      <></>
                      // <a
                      //   href="/login"
                      //   className={styles.mobileLoginBtn}
                      //   onClick={closeMobileMenu}
                      // >
                      //   ورود / ثبت نام
                      // </a>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
}
