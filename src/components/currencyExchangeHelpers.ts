// Helper functions for CurrencyExchange
import { Currency } from '@/types/currency';
import { CurrencyService } from '@/services/currencyService';

// Map currency names to appropriate flags
export const flagMap: { [key: string]: string } = {
  'toman': '🇮🇷',
  'lira': '🇹🇷',
  'usd': '🇺🇸',
  'usdt': '',
  // 'euro': '🇪🇺',
};

export function getCurrencyDisplayInfo(currencyName: string, currencies: Currency[]) {
  const currency = currencies.find(c => c.name === currencyName);
  if (currency) {
    return {
      name: `${currency.fa} (${currency.name.toUpperCase()})`,
      flag: flagMap[currency.name.toLowerCase()] || '💱',
    };
  }
  return {
    name: currencyName.toUpperCase(),
    flag: '💱',
  };
}

// export function calculateExchangeRate(fromCurrency: string, toCurrency: string, currencies: Currency[]): number | null {
//   const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
//   const toCurrencyData = currencies.find(c => c.name === toCurrency);
//   if (fromCurrencyData && toCurrencyData) {
//     let fromRate: number, toRate: number;
//     if (fromCurrency === 'toman') {
//       fromRate = fromCurrencyData.sellPrice || 1;
//     } else {
//       fromRate = fromCurrencyData.sellPrice || 1;
//     }
//     if (toCurrency === 'toman') {
//       toRate = toCurrencyData.buyPrice || 1;
//     } else {
//       toRate = toCurrencyData.sellPrice || 1;
//     }
//     if (fromRate > 0 && toRate > 0) {
//       return fromRate / toRate;
//     }
//   }
//   return null;
// }

// Fetch currencies from API
export async function fetchCurrencies(): Promise<Currency[]> {
  try {
    const fetchedCurrencies = await CurrencyService.getCurrencies();
    return fetchedCurrencies;
  } catch (err) {
    throw new Error(err instanceof Error ? err.message : 'Failed to load currencies');
  }
}

// Currency conversion utility
export function convertCurrency(
  amount: string,
  fromCurrency: string,
  toCurrency: string,
  currencies: Currency[],
  isSell: boolean = false
): string {
  const value = amount.replace(/,/g, '');
  if (isNaN(Number(value))) return '0';

  const fromCurrencyData = currencies.find(c => c.name === fromCurrency);
  const toCurrencyData = currencies.find(c => c.name === toCurrency);

  if (!fromCurrencyData || !toCurrencyData) return '0';

  let rate: number;
  if (toCurrency === 'toman') {
    rate = fromCurrencyData.buyPrice;
  } else {
    rate = toCurrencyData.sellPrice ;
  }


  let converted = Number(value) * rate;
  if(!isSell) {
    converted = Number(value) / rate;
  }

  if (isSell && toCurrency !== 'toman') {
    // For reverse conversion to non-toman, use toLocaleString with formatting
    return Number.isInteger(converted)
      ? converted.toLocaleString()
      : converted.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  } else {
    // For forward conversion or to toman, use simple formatting
    return Number.isInteger(converted) ? String(converted) : converted.toFixed(2);
  }
}

// Format amount with proper locale formatting
export function formatAmount(value: string): string {
  const numValue = value.replace(/,/g, '');
  if (!isNaN(Number(numValue))) {
    return Number(numValue).toLocaleString();
  }
  return value;
}

// Get default currencies for initialization
export function getDefaultCurrencies(currencies: Currency[]): { from: string; to: string } {
  if (currencies.length === 0) return { from: 'toman', to: 'toman' };

  const tomanCurrency = currencies.find(c => c.name === 'toman');
  const liraCurrency = currencies.find(c => c.name === 'lira');

  if (tomanCurrency && liraCurrency) {
    return { from: tomanCurrency.name, to: liraCurrency.name };
  } else if (currencies.length >= 2) {
    return { from: currencies[0].name, to: currencies[1].name };
  }

  return { from: 'toman', to: 'toman' };
}

// Event handler creators
export function createSwapHandler(
  fromCurrency: string,
  toCurrency: string,
  fromAmount: string,
  toAmount: string,
  setFromCurrency: (currency: string) => void,
  setToCurrency: (currency: string) => void,
  setFromAmount: (amount: string) => void,
  setToAmount: (amount: string) => void
) {
  return () => {
    setFromCurrency(toCurrency);
    setToCurrency(fromCurrency);
    setFromAmount(toAmount);
    setToAmount(fromAmount);
  };
}

export function createDropdownToggleHandler(
  isOpen: boolean,
  setThisOpen: (open: boolean) => void,
  setOtherOpen: (open: boolean) => void
) {
  return () => {
    setThisOpen(!isOpen);
    setOtherOpen(false);
  };
}

export function createCurrencySelectHandler(
  setCurrency: (currency: string) => void,
  setDropdownOpen: (open: boolean) => void
) {
  return (currencyName: string) => {
    setCurrency(currencyName);
    setDropdownOpen(false);
  };
}

export function createAmountChangeHandler(
  fromCurrency: string,
  toCurrency: string,
  currencies: Currency[],
  setFromAmount: (amount: string) => void,
  setToAmount: (amount: string) => void,
  isReverse: boolean = false
) {
  return (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/,/g, '');
    if (!isNaN(Number(value))) {
      if (isReverse) {
        setToAmount(value);
        const convertedAmount = convertCurrency(value, fromCurrency, toCurrency, currencies, true);
        setFromAmount(convertedAmount);
      } else {
        setFromAmount(Number(value).toLocaleString());
        const convertedAmount = convertCurrency(value, fromCurrency, toCurrency, currencies, false);
        setToAmount(convertedAmount);
      }
    }
  };
}

// Filter currencies for dropdown based on current selection
export function getFilteredCurrencies(currencies: Currency[], fromCurrency: string, isFromDropdown: boolean): Currency[] {
  if (isFromDropdown) {
    return fromCurrency === 'toman'
      ? currencies.filter(currency => currency.name === 'toman')
      : currencies.filter(currency => currency.name !== 'toman');
  } else {
    // For "to" dropdown
    return fromCurrency !== 'toman'
      ? currencies.filter(currency => currency.name === 'toman')
      : currencies.filter(currency => currency.name !== 'toman');
  }
}

// Create click outside handler for dropdowns
export function createClickOutsideHandler(
  setFromDropdownOpen: (open: boolean) => void,
  setToDropdownOpen: (open: boolean) => void
) {
  return (event: MouseEvent) => {
    const target = event.target as Element;
    if (!target.closest('.dropdown-container')) {
      setFromDropdownOpen(false);
      setToDropdownOpen(false);
    }
  };
}
