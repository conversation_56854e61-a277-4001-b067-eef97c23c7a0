'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { getBankCards } from '@/services/bankCardService';
import { OrderService } from '@/services/orderService';
import { CurrencyService } from '@/services/currencyService';
import { BankCard } from '@/types/bank';
import { Currency } from '@/types/currency';
import { ExchangeDetails, OrderRequest } from '@/types/order';
import styles from './OrderForm.module.css';

interface OrderFormData {
  cardId: string;
  depositId: string;
}

export default function OrderForm() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { token } = useAuth();

  // Exchange details from URL parameters
  const [exchangeDetails, setExchangeDetails] = useState<ExchangeDetails | null>(null);
  const [cards, setCards] = useState<BankCard[]>([]);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<OrderFormData>({
    cardId: '',
    depositId: '',
  });

  const [errors, setErrors] = useState<Partial<OrderFormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load exchange details and user data on component mount
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);

        // Get exchange details from URL parameters
        const fromCurrency = searchParams.get('fromCurrency');
        const toCurrency = searchParams.get('toCurrency');
        const fromAmount = searchParams.get('fromAmount');
        const toAmount = searchParams.get('toAmount');
        const fromCurrencyFa = searchParams.get('fromCurrencyFa');
        const toCurrencyFa = searchParams.get('toCurrencyFa');

        if (!fromCurrency || !toCurrency || !fromAmount || !toAmount) {
          setError('اطلاعات تبدیل ارز یافت نشد. لطفاً از صفحه اصلی شروع کنید.');
          return;
        }

        setExchangeDetails({
          fromCurrency,
          toCurrency,
          fromAmount,
          toAmount,
          fromCurrencyFa: fromCurrencyFa || fromCurrency,
          toCurrencyFa: toCurrencyFa || toCurrency,
          exchangeRate: parseFloat(toAmount) / parseFloat(fromAmount),
        });

        // Load currencies and user cards
        const [currenciesData, cardsData] = await Promise.all([
          CurrencyService.getCurrencies(),
          token ? getBankCards(token) : []
        ]);

        setCurrencies(currenciesData);
        setCards(cardsData);

        // Auto-select card if user has only one
        if (cardsData.length === 1) {
          setFormData(prev => ({ ...prev, cardId: cardsData[0].id }));
        }

      } catch (err) {
        console.error('Error loading data:', err);
        setError('خطا در بارگذاری اطلاعات. لطفاً دوباره تلاش کنید.');
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [searchParams, token]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof OrderFormData]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<OrderFormData> = {};

    if (!formData.cardId) {
      newErrors.cardId = 'انتخاب کارت الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm() || !exchangeDetails || !token) {
      return;
    }

    setIsSubmitting(true);

    try {
      const orderData: OrderRequest = {
        cardId: formData.cardId,
        fromCurrency: exchangeDetails.fromCurrency,
        toCurrency: exchangeDetails.toCurrency,
        fromAmount: parseFloat(exchangeDetails.fromAmount),
        toAmount: parseFloat(exchangeDetails.toAmount),
        exchangeRate: exchangeDetails.exchangeRate,
        depositId: formData.depositId || undefined,
      };

      const response = await OrderService.submitOrder(orderData, token);

      if (response.success) {
        alert('سفارش شما با موفقیت ثبت شد!');
        // Reset form
        setFormData({ cardId: '', depositId: '' });
        router.push('/orders');
      } else {
        alert(response.message || 'خطا در ثبت سفارش');
      }

    } catch (error) {
      console.error('Error submitting order:', error);
      alert('خطا در ثبت سفارش. لطفاً دوباره تلاش کنید.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state
  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <div style={{ textAlign: 'center', padding: '2rem' }}>
            <p>در حال بارگذاری اطلاعات...</p>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
            <p>{error}</p>
          </div>
        </div>
      </div>
    );
  }

  // Show form if exchange details are available
  if (!exchangeDetails) {
    return (
      <div className={styles.container}>
        <div className={styles.formCard}>
          <div style={{ textAlign: 'center', padding: '2rem', color: 'red' }}>
            <p>اطلاعات تبدیل ارز یافت نشد. لطفاً از صفحه اصلی شروع کنید.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      {/* Exchange Details Table */}
      <div className={styles.formCard}>
        <h2 className={styles.formTitle}>جزئیات تبدیل ارز</h2>
        <div className={styles.exchangeTable}>
          <table className={styles.table}>
            <tbody>
              <tr>
                <td className={styles.tableLabel}>از ارز:</td>
                <td className={styles.tableValue}>
                  {exchangeDetails.fromCurrencyFa} ({exchangeDetails.fromCurrency.toUpperCase()})
                </td>
              </tr>
              <tr>
                <td className={styles.tableLabel}>به ارز:</td>
                <td className={styles.tableValue}>
                  {exchangeDetails.toCurrencyFa} ({exchangeDetails.toCurrency.toUpperCase()})
                </td>
              </tr>
              <tr>
                <td className={styles.tableLabel}>مبلغ پرداختی:</td>
                <td className={styles.tableValue}>
                  {parseFloat(exchangeDetails.fromAmount).toLocaleString()} {exchangeDetails.fromCurrencyFa}
                </td>
              </tr>
              <tr>
                <td className={styles.tableLabel}>مبلغ دریافتی:</td>
                <td className={styles.tableValue}>
                  {parseFloat(exchangeDetails.toAmount).toLocaleString()} {exchangeDetails.toCurrencyFa}
                </td>
              </tr>
              <tr>
                <td className={styles.tableLabel}>نرخ تبدیل:</td>
                <td className={styles.tableValue}>
                  {exchangeDetails.exchangeRate.toFixed(8)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      {/* Order Form */}
      <div className={styles.formCard}>
        <h2 className={styles.formTitle}>اطلاعات سفارش</h2>

        <form onSubmit={handleSubmit} className={styles.form}>
          {/* Card Selection */}
          <div className={styles.formGroup}>
            <label htmlFor="cardId" className={styles.label}>
              انتخاب کارت بانکی *
            </label>
            {cards.length === 0 ? (
              <p className={styles.noCards}>
                هیچ کارت بانکی یافت نشد. لطفاً ابتدا کارت خود را اضافه کنید.
              </p>
            ) : cards.length === 1 ? (
              <div className={styles.singleCard}>
                <p>کارت انتخاب شده:</p>
                <div className={styles.cardInfo}>
                  <strong>{cards[0].bankName}</strong> -
                  {cards[0].cardNumber.slice(-4).padStart(cards[0].cardNumber.length, '*')}
                </div>
              </div>
            ) : (
              <select
                id="cardId"
                name="cardId"
                value={formData.cardId}
                onChange={handleInputChange}
                className={`${styles.select} ${errors.cardId ? styles.inputError : ''}`}
                disabled={isSubmitting}
              >
                <option value="">کارت خود را انتخاب کنید</option>
                {cards.map((card) => (
                  <option key={card.id} value={card.id}>
                    {card.bankName} - **** **** **** {card.cardNumber.slice(-4)}
                  </option>
                ))}
              </select>
            )}
            {errors.cardId && (
              <span className={styles.errorMessage}>{errors.cardId}</span>
            )}
          </div>

          {/* Optional Deposit ID */}
          <div className={styles.formGroup}>
            <label htmlFor="depositId" className={styles.label}>
              شماره واریز (اختیاری)
            </label>
            <input
              type="text"
              id="depositId"
              name="depositId"
              value={formData.depositId}
              onChange={handleInputChange}
              className={styles.input}
              placeholder="در صورت داشتن شماره واریز وارد کنید"
              disabled={isSubmitting}
            />
          </div>

          <button
            type="submit"
            className={styles.submitButton}
            disabled={isSubmitting || cards.length === 0}
          >
            {isSubmitting ? 'در حال ثبت...' : 'ثبت سفارش'}
          </button>
        </form>
      </div>
    </div>
  );
}
