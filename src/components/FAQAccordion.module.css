.faqAccordion {
  margin: 48px auto 0 auto;
  max-width: 80%;
  background: #fff;
  border-radius: 16px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.07);
  padding: 32px 24px;
  direction: rtl;
}
.faqTitle {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 24px;
  text-align: center;
  color: #1a237e;
}
.faqItem {
  border-bottom: 1px solid #eee;
}
.faqQuestion {
  width: 100%;
  background: none;
  border: none;
  outline: none;
  font-size: 1.1rem;
  font-weight: 500;
  text-align: right;
  padding: 16px 0;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #222;
  transition: color 0.2s;
}
.faqQuestion:hover {
  color: #3949ab;
}
.arrow {
  font-size: 1.2rem;
  margin-right: 8px;
}
.faqAnswer {
  padding: 0 0 16px 0;
  color: #444;
  font-size: 1rem;
  line-height: 1.7;
  background: #f7f8fa;
  border-radius: 8px;
  margin-top: -8px;
}

@media (width <= 768px) {
  .FAQAccordion-module__sbNgYW__faqAccordion {
    margin: 5%;
  }
}
