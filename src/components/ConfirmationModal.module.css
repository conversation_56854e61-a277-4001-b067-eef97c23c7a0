.overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.danger .icon {
  background: rgba(244, 67, 54, 0.1);
  color: #f44336;
}

.warning .icon {
  background: rgba(255, 193, 7, 0.1);
  color: #ffc107;
}

.info .icon {
  background: rgba(33, 150, 243, 0.1);
  color: #2196f3;
}

.title {
  font-size: 1.3rem;
  font-weight: bold;
  margin: 0;
  color: #333;
}

.content {
  margin-bottom: 2rem;
}

.message {
  color: #666;
  line-height: 1.6;
  margin: 0;
  font-size: 1rem;
}

.actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancelButton,
.confirmButton {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
}

.cancelButton {
  background: #f5f5f5;
  color: #666;
  border: 1px solid #e0e0e0;
}

.cancelButton:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.confirmButton {
  color: white;
}

.dangerButton {
  background: #f44336;
}

.dangerButton:hover {
  background: #d32f2f;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.3);
}

.warningButton {
  background: #ffc107;
  color: #333;
}

.warningButton:hover {
  background: #ffb300;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
}

.infoButton {
  background: #2196f3;
}

.infoButton:hover {
  background: #1976d2;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.3);
}

@media (max-width: 480px) {
  .modal {
    padding: 1.5rem;
    margin: 1rem;
  }
  
  .actions {
    flex-direction: column;
  }
  
  .cancelButton,
  .confirmButton {
    width: 100%;
  }
}

@media (prefers-color-scheme: dark) {
  .modal {
    background: #1a1a1a;
    color: #e5e5e5;
  }
  
  .title {
    color: #e5e5e5;
  }
  
  .message {
    color: #ccc;
  }
  
  .cancelButton {
    background: #333;
    color: #e5e5e5;
    border-color: #555;
  }
  
  .cancelButton:hover {
    background: #444;
  }
  
  .dangerButton {
    background: #ff6b6b;
  }
  
  .dangerButton:hover {
    background: #ff5252;
  }
}
