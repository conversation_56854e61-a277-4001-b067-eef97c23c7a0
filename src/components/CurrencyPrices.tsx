'use client';

import { useState, useEffect } from 'react';
import { Currency } from '@/types/currency';
import { CurrencyService } from '@/services/currencyService';
import { flagMap } from './currencyExchangeHelpers';
import styles from './CurrencyPrices.module.css';

export default function CurrencyPrices() {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Fetch currencies function
  const fetchCurrencyPrices = async () => {
    try {
      setError(null);
      const fetchedCurrencies = await CurrencyService.getCurrencies();
      // Filter out toman as it's the base currency
      const filteredCurrencies = fetchedCurrencies.filter(currency => currency.name !== 'toman');
      setCurrencies(filteredCurrencies);
      setLastUpdated(new Date());
    } catch (err) {
      setError(err instanceof Error ? err.message : 'خطا در بارگذاری قیمت ارزها');
      console.error('Error fetching currency prices:', err);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    fetchCurrencyPrices();
  }, []);

  // Auto-refresh every 30 seconds for real-time updates
  useEffect(() => {
    const interval = setInterval(() => {
      fetchCurrencyPrices();
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, []);

  // Format price with thousand separators
  const formatPrice = (price: number): string => {
    return price.toLocaleString('fa-IR');
  };

  // Format last updated time
  const formatLastUpdated = (date: Date): string => {
    return date.toLocaleTimeString('fa-IR', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>قیمت لحظه‌ای ارزها</h2>
        </div>
        <div className={styles.loading}>
          <p>در حال بارگذاری قیمت ارزها...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.container}>
        <div className={styles.header}>
          <h2 className={styles.title}>قیمت لحظه‌ای ارزها</h2>
        </div>
        <div className={styles.error}>
          <p>{error}</p>
          <button onClick={fetchCurrencyPrices} className={styles.retryBtn}>
            تلاش مجدد
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>قیمت لحظه‌ای ارزها</h2>
        {/* {lastUpdated && (
          <div className={styles.lastUpdated}>
            آخرین بروزرسانی: {formatLastUpdated(lastUpdated)}
          </div>
        )} */}
      </div>

      <div className={styles.pricesGrid}>
        {currencies.map((currency) => (
          <div key={currency._id} className={styles.currencyCard}>
            <div className={styles.currencyHeader}>
              <div className={styles.currencyInfo}>
                <span className={styles.flag}>
                  {flagMap[currency.name.toLowerCase()] || '💱'}
                </span>
                <div className={styles.currencyNames}>
                  <span className={styles.currencyNameFa}>{currency.fa}</span>
                  <span className={styles.currencyNameEn}>{currency.name.toUpperCase()}</span>
                </div>
              </div>
            </div>
            
            <div className={styles.pricesContainer}>
              <div className={styles.priceItem}>
                <span className={styles.priceLabel}>قیمت خرید</span>
                <span className={styles.priceValue}>
                  {formatPrice(currency.buyPrice)} تومان
                </span>
              </div>
              
              <div className={styles.priceItem}>
                <span className={styles.priceLabel}>قیمت فروش</span>
                <span className={styles.priceValue}>
                  {formatPrice(currency.sellPrice)} تومان
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className={styles.refreshNote}>
        <p>قیمت‌ها هر 10 دقیقه به‌روزرسانی می‌شوند</p>
      </div>
    </div>
  );
}
