"use client";

import { useState } from "react";
import styles from "./FAQAccordion.module.css";

const faqData = [
  {
    question: "چگونه می‌توانم ارز خود را تبدیل کنم؟",
    answer: "از قسمت تبدیل ارز می توانید برای شروع ثبت سفارش خود استفاده کنید",
  },
  {
    question: "کارمزد تبدیل ارز چقدر است؟",
    answer: "شما بابت تبدیل ارز کارمزدی نمی پردازید.",
  },
  {
    question: "چه مدت زمانی طول می‌کشد تا تبدیل انجام شود؟",
    answer:
      "تبدیل ارز معمولاً در کمتر از یک ساعت انجام می‌شود، اما بسته به شرایط بانکی ممکن است بیشتر طول بکشد.",
  },
  {
    question: "آیا امکان پیگیری وضعیت سفارش وجود دارد؟",
    answer:
      "بله در منوی کاربری از قسمت سفارشات می توانید وضعیت سفارش خود را پیگیری کنید.",
  },
];

const FAQAccordion = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(null);

  const toggleAccordion = (idx: number) => {
    setOpenIndex(openIndex === idx ? null : idx);
  };

  return (
    <div className={styles.faqAccordion}>
      <h2 className={styles.faqTitle}>سوالات متداول</h2>
      {faqData.map((item, idx) => (
        <div key={idx} className={styles.faqItem}>
          <button
            className={styles.faqQuestion}
            onClick={() => toggleAccordion(idx)}
            aria-expanded={openIndex === idx}
          >
            {item.question}
            <span className={styles.arrow}>
              {openIndex === idx ? "▲" : "▼"}
            </span>
          </button>
          {openIndex === idx && (
            <div className={styles.faqAnswer}>{item.answer}</div>
          )}
        </div>
      ))}
    </div>
  );
};

export default FAQAccordion;
