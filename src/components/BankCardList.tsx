import React from 'react';
import { BankCard } from '../types/bank';
import styles from './BankCardList.module.css';

interface BankCardListProps {
  cards: BankCard[];
  onEdit: (card: BankCard) => void;
  onDelete: (id: string) => void;
}

const BankCardList: React.FC<BankCardListProps> = ({ cards, onEdit, onDelete }) => {
  const formatCardNumber = (cardNumber: string) => {
    // Mask the card number for security (show only last 4 digits)
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.length >= 4) {
      const lastFour = cleaned.slice(-4);
      const masked = '**** **** **** ' + lastFour;
      return masked;
    }
    return cardNumber;
  };

  const formatIban = (iban: string) => {
    // Format IBAN for better readability
    return iban.replace(/(.{4})/g, '$1 ').trim();
  };

  const getBankCardGradient = (bankName: string) => {
    // Different gradients for different banks
    const gradients = {
      'ملی': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
      'صادرات': 'linear-gradient(135deg, #134e5e 0%, #71b280 100%)',
      'تجارت': 'linear-gradient(135deg, #fc466b 0%, #3f5efb 100%)',
      'کشاورزی': 'linear-gradient(135deg, #fdbb2d 0%, #22c1c3 100%)',
      'پاسارگاد': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      'سامان': 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      'پارسیان': 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      'default': 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)'
    };

    return gradients[bankName as keyof typeof gradients] || gradients.default;
  };

  return (
    <div className={styles.cardListContainer}>
      <h2 className={styles.sectionTitle}>کارت‌های بانکی شما</h2>
      {cards.length === 0 ? (
        <div className={styles.noCardsMessage}>
          <div className={styles.noCardsIcon}>💳</div>
          <div className={styles.noCardsText}>
            هنوز کارت بانکی اضافه نشده است.<br />
            برای شروع، کارت بانکی جدید اضافه کنید.
          </div>
        </div>
      ) : (
        <ul className={styles.cardGrid}>
          {cards.map((card, index) => (
            <li key={card.id || `card-${index}-${card.cardNumber}`} className={styles.bankCard} style={{ background: getBankCardGradient(card.bankName) }}>
              <div className={styles.cardHeader}>
                <div className={styles.bankName}>{card.bankName}</div>
                <div className={styles.cardType}>بانک</div>
              </div>

              <div className={styles.cardNumber}>
                {formatCardNumber(card.cardNumber)}
              </div>

              <div className={styles.cardFooter}>
                <div className={styles.accountHolder}>
                  <div className={styles.accountHolderLabel}>نام صاحب حساب</div>
                  <div className={styles.accountHolderName}>{card.accountHolderName}</div>
                </div>
              </div>

              <div className={styles.ibanInfo}>
                <div className={styles.ibanLabel}>شماره شبا</div>
                <div className={styles.ibanNumber}>{formatIban(card.iban)}</div>
              </div>

              <div className={styles.cardActions}>
                <button
                  onClick={() => onEdit(card)}
                  className={`${styles.actionButton} ${styles.editButton}`}
                  title="ویرایش کارت"
                >
                  ✏️ ویرایش
                </button>
                <button
                  onClick={() => card.id && onDelete(card.id)}
                  className={`${styles.actionButton} ${styles.deleteButton}`}
                  title="حذف کارت"
                  disabled={!card.id}
                >
                  🗑️ حذف
                </button>
              </div>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};

export default BankCardList;
