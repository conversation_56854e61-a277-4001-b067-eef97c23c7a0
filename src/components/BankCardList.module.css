.cardListContainer {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.sectionTitle {
  font-size: 1.8rem;
  font-weight: bold;
  color: #d32f2f;
  text-align: center;
  margin-bottom: 2rem;
}

.noCardsMessage {
  background: white;
  border: 2px dashed #d32f2f;
  border-radius: 12px;
  padding: 3rem 2rem;
  text-align: center;
  margin: 2rem auto;
  max-width: 500px;
}

.noCardsIcon {
  font-size: 4rem;
  color: #d32f2f;
  margin-bottom: 1rem;
}

.noCardsText {
  color: #666;
  font-size: 1.1rem;
  line-height: 1.6;
}

.cardGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
  padding: 0;
  margin: 0;
  list-style: none;
}

.bankCard {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  border-radius: 16px;
  padding: 1.5rem;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.bankCard:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.3);
}

.bankCard::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transform: rotate(45deg);
  transition: all 0.5s ease;
}

.bankCard:hover::before {
  animation: shine 0.5s ease-in-out;
}

@keyframes shine {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.cardHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.bankName {
  font-size: 1.1rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.cardType {
  font-size: 0.9rem;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

.cardNumber {
  font-family: 'Courier New', monospace;
  font-size: 1.2rem;
  font-weight: bold;
  letter-spacing: 2px;
  margin: 1rem 0;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.cardFooter {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.accountHolder {
  flex: 1;
}

.accountHolderLabel {
  font-size: 0.7rem;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.accountHolderName {
  font-size: 0.9rem;
  font-weight: bold;
  text-transform: uppercase;
}

.cardActions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.actionButton {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  backdrop-filter: blur(10px);
}

.actionButton:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.editButton {
  background: rgba(76, 175, 80, 0.3);
  border-color: rgba(76, 175, 80, 0.5);
}

.editButton:hover {
  background: rgba(76, 175, 80, 0.5);
}

.deleteButton {
  background: rgba(244, 67, 54, 0.3);
  border-color: rgba(244, 67, 54, 0.5);
}

.deleteButton:hover:not(:disabled) {
  background: rgba(244, 67, 54, 0.5);
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.actionButton:disabled:hover {
  transform: none;
  background: rgba(255, 255, 255, 0.2);
}

.ibanInfo {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.ibanLabel {
  font-size: 0.7rem;
  opacity: 0.7;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.ibanNumber {
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  opacity: 0.9;
}

@media (max-width: 768px) {
  .cardGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .bankCard {
    margin: 0 0.5rem;
  }

  .noCardsMessage {
    margin: 1rem;
    padding: 2rem 1rem;
  }

  .noCardsIcon {
    font-size: 3rem;
  }
}

@media (prefers-color-scheme: dark) {
  .sectionTitle {
    color: #ff6b6b;
  }

  .noCardsMessage {
    background: #1a1a1a;
    border-color: #ff6b6b;
  }

  .noCardsIcon {
    color: #ff6b6b;
  }

  .noCardsText {
    color: #ccc;
  }
}
