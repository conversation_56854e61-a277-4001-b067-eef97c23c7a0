'use client';

import { useState } from 'react';
import { Order } from '@/types/order';
import styles from './OrderCard.module.css';

interface OrderCardProps {
  order: Order;
  getCurrencyDisplayName: (currencyName: string) => string;
}

export default function OrderCard({ order, getCurrencyDisplayName }: OrderCardProps) {
  const [showDetails, setShowDetails] = useState(false);

  const getStatusText = (status: string): string => {
    const statusMap: Record<string, string> = {
      pending: 'در انتظار',
      processing: 'در حال پردازش',
      completed: 'تکمیل شده',
      cancelled: 'لغو شده',
      failed: 'ناموفق'
    };
    return statusMap[status] || status;
  };

  const getStatusColor = (status: string): string => {
    const colorMap: Record<string, string> = {
      pending: '#ffa726',
      processing: '#42a5f5',
      completed: '#66bb6a',
      cancelled: '#ef5350',
      failed: '#ef5350'
    };
    return colorMap[status] || '#757575';
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString('fa-IR', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatAmount = (amount: number): string => {
    return amount.toLocaleString('fa-IR');
  };

  const toggleDetails = () => {
    setShowDetails(!showDetails);
  };

  return (
    <div className={styles.orderCard}>
      <div className={styles.orderHeader}>
        <div className={styles.orderInfo}>
          <div className={styles.orderTitle}>
            <span className={styles.orderNumber}>سفارش #{order._id.slice(-8)}</span>
            <span 
              className={styles.orderStatus}
              style={{ backgroundColor: getStatusColor(order.status) }}
            >
              {getStatusText(order.status)}
            </span>
          </div>
          <div className={styles.orderDate}>
            {formatDate(order.createdAt)}
          </div>
        </div>
        <button
          onClick={toggleDetails}
          className={styles.toggleButton}
          aria-expanded={showDetails}
        >
          {showDetails ? '▲' : '▼'}
        </button>
      </div>

      <div className={styles.orderSummary}>
        <div className={styles.exchangeInfo}>
          <div className={styles.exchangeItem}>
            <span className={styles.label}>از:</span>
            <span className={styles.value}>
              {formatAmount(order.fromAmount)} {getCurrencyDisplayName(order.fromCurrency)}
            </span>
          </div>
          <div className={styles.exchangeArrow}>←</div>
          <div className={styles.exchangeItem}>
            <span className={styles.label}>به:</span>
            <span className={styles.value}>
              {formatAmount(order.toAmount)} {getCurrencyDisplayName(order.toCurrency)}
            </span>
          </div>
        </div>
      </div>

      {showDetails && (
        <div className={styles.orderDetails}>
          <div className={styles.detailsGrid}>
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>نرخ تبدیل:</span>
              <span className={styles.detailValue}>{order.exchangeRate.toFixed(4)}</span>
            </div>
            
            {order.cardInfo && (
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>کارت بانکی:</span>
                <span className={styles.detailValue}>
                  {order.cardInfo.bankName} - **** {order.cardInfo.cardNumber}
                </span>
              </div>
            )}
            
            {order.depositId && (
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>شماره واریز:</span>
                <span className={styles.detailValue}>{order.depositId}</span>
              </div>
            )}
            
            <div className={styles.detailItem}>
              <span className={styles.detailLabel}>تاریخ ایجاد:</span>
              <span className={styles.detailValue}>{formatDate(order.createdAt)}</span>
            </div>
            
            {order.updatedAt !== order.createdAt && (
              <div className={styles.detailItem}>
                <span className={styles.detailLabel}>آخرین بروزرسانی:</span>
                <span className={styles.detailValue}>{formatDate(order.updatedAt)}</span>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
