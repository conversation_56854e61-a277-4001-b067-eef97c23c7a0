.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 2rem;
  font-weight: bold;
  color: #fff;
  margin-bottom: 0.5rem;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.lastUpdated {
  font-size: 0.9rem;
  color: #666;
  background: #f5f5f5;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  display: inline-block;
}

.loading, .error {
  text-align: center;
  padding: 2rem;
  background: #fff;
  border-radius: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.error p {
  color: #d32f2f;
  margin-bottom: 1rem;
}

.retryBtn {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 10px;
  cursor: pointer;
  font-size: 1rem;
  transition: background-color 0.3s ease;
}

.retryBtn:hover {
  background: #b71c1c;
}

.pricesGrid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.currencyCard {
  background: #fff;
  border: 2px solid #e0e0e0;
  border-radius: 15px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.currencyCard:hover {
  border-color: #c592e3;
  box-shadow: 0 6px 20px rgba(211, 47, 47, 0.15);
  transform: translateY(-2px);
}

.currencyHeader {
  margin-bottom: 1.5rem;
}

.currencyInfo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.flag {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  border: 2px solid #e0e0e0;
}

.currencyNames {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.currencyNameFa {
  font-size: 1.2rem;
  font-weight: bold;
  color: #333;
}

.currencyNameEn {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.pricesContainer {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.priceItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 10px;
  border: 1px solid #e9ecef;
}

.priceLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

.priceValue {
  font-size: 1.1rem;
  font-weight: bold;
  color: #0d9923;
  direction: rtl;
  text-align: right;
}

.refreshNote {
  text-align: center;
  margin-top: 1rem;
}

.refreshNote p {
  font-size: 0.85rem;
  color: #888;
  background: #f8f9fa;
  padding: 0.5rem 1rem;
  border-radius: 15px;
  display: inline-block;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .title {
    font-size: 1.5rem;
  }

  .pricesGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .currencyCard {
    padding: 1rem;
  }

  .flag {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
  }

  .currencyNameFa {
    font-size: 1rem;
  }

  .priceItem {
    padding: 0.5rem;
  }

  .priceValue {
    font-size: 1rem;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .currencyCard {
    background: #2c2c2c;
    border-color: #444;
    color: #fff;
  }

  .currencyCard:hover {
    border-color: #ff6b6b;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.15);
  }

  .title {
    color: #ff6b6b;
  }

  .lastUpdated {
    background: #444;
    color: #ccc;
  }

  .loading, .error {
    background: #2c2c2c;
    color: #fff;
  }

  .flag {
    background: #444;
    border-color: #555;
  }

  .currencyNameFa {
    color: #fff;
  }

  .currencyNameEn {
    color: #ccc;
  }

  .priceItem {
    background: #444;
    border-color: #555;
  }

  .priceLabel {
    color: #ccc;
  }

  .priceValue {
    color: #ff6b6b;
  }

  .refreshNote p {
    background: #444;
    color: #ccc;
  }
}
