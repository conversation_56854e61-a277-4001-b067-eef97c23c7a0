import React, { useState, useEffect } from 'react';
import { BankCard } from '../types/bank';
import styles from './BankCardForm.module.css';

interface BankCardFormProps {
  card?: BankCard;
  onSubmit: (card: Omit<BankCard, 'id'> | BankCard) => void;
  onCancel: () => void;
}

const BankCardForm: React.FC<BankCardFormProps> = ({ card, onSubmit, onCancel }) => {
  const [cardNumber, setCardNumber] = useState('');
  const [bankName, setBankName] = useState('');
  const [accountHolderName, setAccountHolderName] = useState('');
  const [iban, setIban] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const iranianBanks = [
    'ملی',
    'صادرات',
    'تجارت',
    'کشاورزی',
    'پاسارگاد',
    'سامان',
    'پارسیان',
    'ملت',
    'رفاه',
    'توسعه تعاون',
    'اقتصاد نوین',
    'کارآفرین',
    'سینا',
    'شهر',
    'دی',
    'ایران زمین',
    'قوامین',
    'حکمت ایرانیان',
    'گردشگری',
    'صنعت و معدن',
    'مهر اقتصاد',
    'مهر ایران',
    'نور',
    'کوثر',
    'مسکن',
    'پست بانک',
    'توسعه صادرات',
    'آینده'
  ];

  useEffect(() => {
    if (card) {
      setCardNumber(card.cardNumber);
      setBankName(card.bankName);
      setAccountHolderName(card.accountHolderName);
      setIban(card.iban);
    } else {
      setCardNumber('');
      setBankName('');
      setAccountHolderName('');
      setIban('');
    }
  }, [card]);

  const formatCardNumber = (value: string) => {
    // Remove all non-digits
    const cleaned = value.replace(/\D/g, '');
    // Add spaces every 4 digits
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ');
    return formatted;
  };

  const formatIban = (value: string) => {
    // Remove all non-alphanumeric characters
    let cleaned = value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();

    // Ensure it starts with IR for Iranian IBANs
    if (cleaned && !cleaned.startsWith('IR')) {
      cleaned = 'IR' + cleaned;
    }

    // Add spaces every 4 characters for readability
    const formatted = cleaned.replace(/(.{4})/g, '$1 ').trim();
    return formatted;
  };

  const handleCardNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cleaned = value.replace(/\D/g, '');

    // Limit to 16 digits
    if (cleaned.length <= 16) {
      setCardNumber(cleaned);
    }
  };

  const handleIbanChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    const cleaned = value.replace(/[^a-zA-Z0-9]/g, '').toUpperCase();

    // Limit to 26 characters (IR + 24 digits for Iranian IBAN)
    if (cleaned.length <= 26) {
      setIban(cleaned);
    }
  };

  const validateForm = () => {
    if (!cardNumber || cardNumber.length !== 16) {
      alert('شماره کارت باید 16 رقم باشد');
      return false;
    }

    if (!bankName.trim()) {
      alert('نام بانک الزامی است');
      return false;
    }

    if (!accountHolderName.trim()) {
      alert('نام صاحب حساب الزامی است');
      return false;
    }

    if (!iban || iban.length < 24) {
      alert('شماره شبا باید حداقل 24 کاراکتر باشد');
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const newCard = {
        cardNumber: cardNumber.trim(),
        bankName: bankName.trim(),
        accountHolderName: accountHolderName.trim(),
        iban: iban.trim()
      };

      if (card) {
        onSubmit({ ...newCard, id: card.id });
      } else {
        onSubmit(newCard);
      }
    } catch (error) {
      console.error('Error submitting form:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <h2 className={styles.formTitle}>
          {card ? 'ویرایش کارت بانکی' : 'افزودن کارت بانکی جدید'}
        </h2>

        <form onSubmit={handleSubmit} className={styles.form}>
          <div className={styles.formRow}>
            <div className={styles.formGroup}>
              <label htmlFor="cardNumber" className={styles.label}>
                شماره کارت
              </label>
              <input
                type="text"
                id="cardNumber"
                value={formatCardNumber(cardNumber)}
                onChange={handleCardNumberChange}
                className={`${styles.input} ${styles.cardNumberInput}`}
                placeholder="1234 5678 9012 3456"
                maxLength={19} // 16 digits + 3 spaces
                required
                disabled={isSubmitting}
              />
              <div className={styles.inputHint}>
                شماره کارت 16 رقمی خود را وارد کنید
              </div>
            </div>

            <div className={styles.formGroup}>
              <label htmlFor="bankName" className={styles.label}>
                نام بانک
              </label>
              <select
                id="bankName"
                value={bankName}
                onChange={(e) => setBankName(e.target.value)}
                className={styles.bankSelect}
                required
                disabled={isSubmitting}
              >
                <option value="">انتخاب بانک</option>
                {iranianBanks.map((bank) => (
                  <option key={bank} value={bank}>
                    بانک {bank}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="accountHolderName" className={styles.label}>
              نام صاحب حساب
            </label>
            <input
              type="text"
              id="accountHolderName"
              value={accountHolderName}
              onChange={(e) => setAccountHolderName(e.target.value)}
              className={styles.input}
              placeholder="نام و نام خانوادگی صاحب حساب"
              required
              disabled={isSubmitting}
            />
          </div>

          <div className={styles.formGroup}>
            <label htmlFor="iban" className={styles.label}>
              شماره شبا
            </label>
            <input
              type="text"
              id="iban"
              value={formatIban(iban)}
              onChange={handleIbanChange}
              className={`${styles.input} ${styles.ibanInput}`}
              placeholder="IR12 3456 7890 1234 5678 9012 34"
              maxLength={31} // IR + 24 digits + 6 spaces
              required
              disabled={isSubmitting}
            />
            <div className={styles.inputHint}>
              شماره شبا که با IR شروع می شود را وارد کنید (24 رقم)   
                       </div>
          </div>

          <div className={styles.actions}>
            <button
              type="submit"
              className={styles.submitButton}
              disabled={isSubmitting}
            >
              {isSubmitting ? 'در حال ذخیره...' : (card ? 'بروزرسانی کارت' : 'افزودن کارت')}
            </button>
            <button
              type="button"
              onClick={onCancel}
              className={styles.cancelButton}
              disabled={isSubmitting}
            >
              لغو
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default BankCardForm;
