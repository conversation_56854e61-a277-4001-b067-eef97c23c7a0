import axios from 'axios';

// API Configuration
export const API_CONFIG = {
  baseUrl: process.env.NEXT_PUBLIC_API_BASE_URL,
  endpoints: {
    currencies: '/currencies',
    sendOtp: '/users/send-otp',
    verifyOtp: '/users/verify-otp',
    user: '/users/',
    profile: '/users/profile/',
    bankCards: '/cards', // Added bankCards endpoint
    orders: '/orders', // Added orders endpoint
  },
} as const;

// Helper function to build full API URLs
export const getApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.baseUrl}${endpoint}`;
};

export const createApiInstance = (token?: string, onError?: (messages: string[]) => void) => {
  const api = axios.create({
    baseURL: API_CONFIG.baseUrl,
    headers: {
      'Content-Type': 'application/json',
    },
  });

  // Add a request interceptor to include the auth token
  api.interceptors.request.use(
    (config) => {
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );

  // Add a response interceptor to handle errors
  api.interceptors.response.use(
    (response) => response,
    (error) => {
      if (error.response && error.response.data && error.response.data.message) {
        if (onError) {
          onError(Array.isArray(error.response.data.message) ? error.response.data.message : [error.response.data.message]);
        }
      }
      return Promise.reject(error);
    }
  );

  return api;
};

export default createApiInstance();
