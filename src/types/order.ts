export interface OrderRequest {
  cardId: string;
  fromCurrency: string;
  toCurrency: string;
  fromAmount: number;
  toAmount: number;
  exchangeRate: number;
  depositId?: string;
}

export interface OrderResponse {
  success: boolean;
  message: string;
  data?: {
    id: string;
    status: string;
    createdAt: string;
  };
}

export interface ExchangeDetails {
  fromCurrency: string;
  toCurrency: string;
  fromAmount: string;
  toAmount: string;
  exchangeRate: number;
  fromCurrencyFa: string;
  toCurrencyFa: string;
}

export interface Order {
  _id: any;
  id: string;
  cardId: string;
  fromCurrency: string;
  toCurrency: string;
  fromAmount: number;
  toAmount: number;
  exchangeRate: number;
  depositId?: string;
  status: 'pending' | 'processing' | 'completed' | 'cancelled' | 'failed';
  createdAt: string;
  updatedAt: string;
  // Additional fields that might come from the API
  cardInfo?: {
    bankName: string;
    cardNumber: string; // Last 4 digits
  };
  currencyInfo?: {
    fromCurrencyFa: string;
    toCurrencyFa: string;
  };
}

export interface OrderListResponse {
  count: number;
  success: boolean;
  message: string;
  data: Order[]
}

export interface OrderDetailsResponse {
  success: boolean;
  message: string;
  data?: Order;
}
