// Authentication related types

export interface User {
  id: string;
  mobileNumber: string;
  isActive: boolean;
  lastLoginAt: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthData {
  user: User;
  accessToken: string;
  tokenType: string;
}

export interface SendOtpRequest {
  mobileNumber: string;
}

export interface SendOtpResponse {
  success: boolean;
  message: string;
  data?: {
    expiresIn?: number;
  };
}

export interface VerifyOtpRequest {
  mobileNumber: string;
  code: string;
}

export interface VerifyOtpResponse {
  success: boolean;
  message: string;
  data: AuthData | null;
}

// User Profile types
export interface UserProfile {
  name: string;
  familyName: string;
  nationalCode: string;
  state: string;
  city: string;
  address: string;
  nationalCardImagePath: string;
  authImagePath: string;
  approvalStatus?: 'pending' | 'approved' | 'rejected' | 'not_submitted'; // Add approvalStatus
}

export interface UpdateProfileRequest {
  name?: string;
  familyName?: string;
  nationalCode?: string;
  state?: string;
  city?: string;
  address?: string;
  nationalCardImage?: File | string;
  authImage?: File | string;
}

export interface UpdateProfileResponse {
  success: boolean;
  message: string;
  data?: UserProfile;
}

export interface GetProfileResponse {
  success: boolean;
  message: string;
  data?: UserProfile;
}

export interface AuthContextType {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (authData: AuthData) => void;
  logout: () => void;
}
