import { API_CONFIG, getApiUrl } from '@/config/api';
import { UpdateProfileRequest, UpdateProfileResponse, GetProfileResponse } from '@/types/auth';

export class ProfileService {
  /**
   * Get user profile information
   */
  static async getProfile(token: string): Promise<GetProfileResponse> {
    try {
      const response = await fetch(getApiUrl(API_CONFIG.endpoints.user), {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: GetProfileResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error fetching profile:', error);
      throw new Error('Failed to fetch profile. Please try again.');
    }
  }

  /**
   * Update user profile information
   */
  static async updateProfile(data: UpdateProfileRequest, token: string): Promise<UpdateProfileResponse> {
    try {
      const formData = new FormData();

      for (const key in data) {
        if (Object.prototype.hasOwnProperty.call(data, key)) {
          const value = (data as any)[key];
          if (value instanceof File) {
            formData.append(key, value, value.name);
          } else if (value !== undefined && value !== null) {
            formData.append(key, value.toString());
          }
        }
      }

      const response = await fetch(getApiUrl(API_CONFIG.endpoints.profile), {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: UpdateProfileResponse = await response.json();
      return result;
    } catch (error) {
      console.error('Error updating profile:', error);
      throw new Error('Failed to update profile. Please try again.');
    }
  }
}
