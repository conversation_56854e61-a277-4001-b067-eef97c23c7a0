import { BankCard } from '../types/bank';
import { createApiInstance, API_CONFIG } from '../config/api';

export const getBankCards = async (token: string, onError?: (messages: string[]) => void): Promise<BankCard[]> => {
  const api = createApiInstance(token, onError);
  const response = await api.get<{data: BankCard[]}>(API_CONFIG.endpoints.bankCards);
  return response.data.data;
};

export const addBankCard = async (card: Omit<BankCard, 'id'>, token: string, onError?: (messages: string[]) => void): Promise<BankCard> => {
  const api = createApiInstance(token, onError);
  const response = await api.post<BankCard>(API_CONFIG.endpoints.bankCards, card);
  return response.data;
};

export const updateBankCard = async (card: BankCard & { id: string }, token: string, onError?: (messages: string[]) => void): Promise<BankCard> => {
  const api = createApiInstance(token, onError);
  // Exclude id from body
  const { id, ...cardData } = card;
  const response = await api.patch<BankCard>(`${API_CONFIG.endpoints.bankCards}/${id}`, cardData);
  return response.data;
};

export const deleteBankCard = async (id: string, token: string, onError?: (messages: string[]) => void): Promise<void> => {
  const api = createApiInstance(token, onError);
  await api.delete(`${API_CONFIG.endpoints.bankCards}/${id}`);
};
