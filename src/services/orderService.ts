import { OrderRequest, OrderResponse, OrderListResponse, OrderDetailsResponse } from '@/types/order';
import { createApiInstance, API_CONFIG } from '@/config/api';

export class OrderService {
  /**
   * Submit a new order
   */
  static async submitOrder(
    orderData: OrderRequest,
    token: string,
    onError?: (messages: string[]) => void
  ): Promise<OrderResponse> {
    try {
      const api = createApiInstance(token, onError);
      const response = await api.post<OrderResponse>(API_CONFIG.endpoints.orders, orderData);
      return response.data;
    } catch (error) {
      console.error('Error submitting order:', error);
      throw new Error('Failed to submit order. Please try again.');
    }
  }

  /**
   * Get user's orders with pagination
   */
  static async getUserOrders(
    token: string | undefined,
    page: number = 1,
    limit: number = 10,
    onError?: (messages: string[]) => void
  ): Promise<OrderListResponse> {
    try {
      const api = createApiInstance(token, onError);
      const response = await api.get<OrderListResponse>(
        `${API_CONFIG.endpoints.orders}?page=${page}&limit=${limit}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching orders:', error);
      throw new Error('Failed to fetch orders. Please try again.');
    }
  }

  /**
   * Get order details by ID
   */
  static async getOrderById(
    orderId: string,
    token: string,
    onError?: (messages: string[]) => void
  ): Promise<OrderDetailsResponse> {
    try {
      const api = createApiInstance(token, onError);
      const response = await api.get<OrderDetailsResponse>(
        `${API_CONFIG.endpoints.orders}/${orderId}`
      );
      return response.data;
    } catch (error) {
      console.error('Error fetching order details:', error);
      throw new Error('Failed to fetch order details. Please try again.');
    }
  }
}
