import React from 'react';
import styles from '../../components/AboutUs.module.css';
import Header from '../../components/Header';
import Footer from '../../components/Footer';

const AboutUsPage: React.FC = () => {
  return (
    <div className={styles.container}>
      <main className={styles.mainContent}>
        <h1 className={styles.title}>درباره ما</h1>
        <p className={styles.description}>
          صرافی ما با سال‌ها تجربه در زمینه تبادلات ارزی، به مشتریان خود خدماتی امن، سریع و مطمئن ارائه می‌دهد.
          هدف ما فراهم آوردن بهترین نرخ‌های ارز و تجربه‌ای بی‌نظیر برای تمامی نیازهای ارزی شماست.
        </p>
        <p className={styles.description}>
          تیم متخصص ما همواره در تلاش است تا با بهره‌گیری از جدیدترین فناوری‌ها و رعایت بالاترین استانداردهای امنیتی،
          اطمینان خاطر شما را در هر تراکنش فراهم آورد. ما به شفافیت، صداقت و رضایت مشتریان خود اهمیت ویژه‌ای می‌دهیم.
        </p>
        <h2 className={styles.subtitle}>چشم‌انداز ما</h2>
        <p className={styles.description}>
          تبدیل شدن به پیشروترین و قابل اعتمادترین صرافی آنلاین در منطقه، با ارائه راهکارهای نوآورانه و دسترسی آسان به خدمات ارزی برای همه.
        </p>
        <h2 className={styles.subtitle}>ارزش‌های ما</h2>
        <ul className={styles.valuesList}>
          <li>امنیت: حفاظت از اطلاعات و دارایی‌های شما در اولویت ماست.</li>
          <li>شفافیت: ارائه اطلاعات کامل و واضح در مورد نرخ‌ها و کارمزدها.</li>
          <li>سرعت: انجام تراکنش‌ها در کمترین زمان ممکن.</li>
          <li>پشتیبانی: ارائه خدمات پشتیبانی ۲۴/۷ برای پاسخگویی به نیازهای شما.</li>
        </ul>
      </main>
    </div>
  );
};

export default AboutUsPage;
