'use client';

import { useState, useEffect } from 'react';
import { Suspense } from 'react';
import OrdersList from '@/components/OrdersList';
import styles from './page.module.css';
import { useAuth } from '@/contexts/AuthContext';
import { OrderService } from '@/services/orderService';
import { CurrencyService } from '@/services/currencyService';
import { Order } from '@/types/order';
import { Currency } from '@/types/currency';

function OrdersListWrapper() {
  const [orders, setOrders] = useState<Order[]>([]);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const { token } = useAuth();

  useEffect(() => {
    const loadData = async () => {
      // if (!token) {
      //   console.log(1);
      //   setError('لطفاً وارد شوید');
      //   setLoading(false);
      //   return;
      // } else {
      //   console.log('Token found in OrdersListWrapper. Token:', token);
      // }

      try {
        setLoading(true);
        
        // Load currencies and orders in parallel
        const [currenciesResponse, ordersResponse] = await Promise.all([
          CurrencyService.getCurrencies(),
          OrderService.getUserOrders(token || undefined, page, 10)
        ]);

        setCurrencies(currenciesResponse);
        
        if (ordersResponse.success && ordersResponse.data) {
          const newOrders = ordersResponse.data;
          const total = ordersResponse.count;
          if (page === 1) {
            setOrders(newOrders);
          } else {
            setOrders(prev => [...prev, ...newOrders]);
          }
          
          setTotalPages(Math.ceil(total / 10));
          setHasMore(page < Math.ceil(total / 10));
        } else {
          setError(ordersResponse.message || 'خطا در بارگذاری سفارشات');
        }
      } catch (err) {
        console.error('Error loading orders:', err);
        setError('خطا در بارگذاری اطلاعات. لطفاً دوباره تلاش کنید.');
      } finally {
        setLoading(false);
      }
    };

    if (token) {
      loadData();
    }
  }, [token, page]); // Added page to dependency array

  const loadMore = () => {
    if (hasMore && !loading) {
      setPage(prev => prev + 1);
    }
  };

  const getCurrencyDisplayName = (currencyName: string): string => {
    const currency = currencies.find(c => c.name === currencyName);
    return currency ? currency.fa : currencyName;
  };

  return (
    <OrdersList
      orders={orders}
      currencies={currencies}
      loading={loading}
      error={error}
      hasMore={hasMore}
      loadMore={loadMore}
      getCurrencyDisplayName={getCurrencyDisplayName}
      page={page}
    />
  );
}

export default function OrdersPage() {
  return (
    <div className={styles.page}>
      <div className={styles.hero}>
        <h1 className={styles.title}>سفارشات من</h1>
        <p className={styles.subtitle}>
          مشاهده و پیگیری سفارشات تبدیل ارز
        </p>
      </div>
      <Suspense fallback={<div>در حال بارگذاری...</div>}>
        <OrdersListWrapper />
      </Suspense>
    </div>
  );
}
