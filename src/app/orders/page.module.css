.page {
  min-height: calc(100vh - 200px);
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.hero {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

@media (max-width: 768px) {
  .page {
    padding: 1rem 0.5rem;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }
}

@media (prefers-color-scheme: dark) {
  .page {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
  }

  .title {
    color: #ff6b6b;
  }

  .subtitle {
    color: #ccc;
  }
}
