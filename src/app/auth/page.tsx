
"use client";
import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import styles from './AuthForm.module.css';
import api from '@/config/api';

const AuthPage = () => {
  const [mobileNumber, setmobileNumber] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [mode, setMode] = useState<'login' | 'register'>('login');
  const router = useRouter();
  const { login } = useAuth();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');
    try {
      let payload: any = { password };
      if (mode === 'register') {
        if (!email) {
          setError('ایمیل الزامی است');
          setLoading(false);
          return;
        }
        if (!mobileNumber) {
          setError('شماره موبایل الزامی است');
          setLoading(false);
          return;
        }
        payload = { email, mobileNumber, password };
      } else {
        // login: user can enter either email or mobileNumber
        if (!email && !mobileNumber) {
          setError('ایمیل یا شماره موبایل را وارد کنید');
          setLoading(false);
          return;
        }
        if (email) payload.email = email;
        if (mobileNumber) payload.mobileNumber = mobileNumber;
      }
      const response = await api.post(`/auth/${mode}`, payload);
      const data = response.data as { user?: any; accessToken?: string; tokenType?: string };
      if (data && data.accessToken && data.user && data.tokenType) {
        login({ user: data.user, accessToken: data.accessToken, tokenType: data.tokenType });
        router.push('/');
      } else {
        setError('ورود یا ثبت نام موفق نبود.');
      }
    } catch (err: any) {
      if (err.response && err.response.data && err.response.data.message) {
        setError(err.response.data.message);
      } else {
        setError(err.message || 'خطایی رخ داده است');
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={styles.container}>
      <div className={styles.formCard}>
        <div className={styles.header}>
          <h2 className={styles.title}>{mode === 'login' ? 'ورود به حساب کاربری' : 'ثبت نام کاربر جدید'}</h2>
          <p className={styles.subtitle}>
            لطفا شماره موبایل و رمز عبور خود را وارد کنید
          </p>
        </div>
        <form onSubmit={handleSubmit} className={styles.form}>
          {mode === 'register' && (
            <div className={styles.inputGroup}>
              <label className={styles.label}>ایمیل</label>
              <input
                type="email"
                value={email}
                onChange={e => setEmail(e.target.value)}
                required={mode === 'register'}
                className={`${styles.input} ${error ? styles.inputError : ''}`}
                dir="ltr"
                placeholder="<EMAIL>"
                disabled={loading}
              />
            </div>
          )}
          <div className={styles.inputGroup}>
            <label className={styles.label}>{mode === 'register' ? 'شماره موبایل' : 'ایمیل یا شماره موبایل'}</label>
            <input
              type={mode === 'register' ? 'tel' : 'text'}
              value={mode === 'register' ? mobileNumber : (email ? email : mobileNumber)}
              onChange={e => {
                if (mode === 'register') setmobileNumber(e.target.value);
                else {
                  // Try to detect if input is email or mobile
                  const val = e.target.value;
                  if (/^\d+$/.test(val)) {
                    setmobileNumber(val);
                    setEmail('');
                  } else {
                    setEmail(val);
                    setmobileNumber('');
                  }
                }
              }}
              required
              className={`${styles.input} ${error ? styles.inputError : ''}`}
              dir="ltr"
              placeholder={mode === 'register' ? '09123456789' : 'ایمیل یا شماره موبایل'}
              disabled={loading}
            />
          </div>
          <div className={styles.inputGroup}>
            <label className={styles.label}>رمز عبور</label>
            <input
              type="password"
              value={password}
              onChange={e => setPassword(e.target.value)}
              required
              className={styles.input}
              disabled={loading}
            />
          </div>
          {error && <span className={styles.error}>{error}</span>}
          <button type="submit" disabled={loading} className={styles.submitBtn}>
            {loading ? 'لطفا صبر کنید...' : mode === 'login' ? 'ورود' : 'ثبت نام'}
          </button>
        </form>
        <div className={styles.footer}>
          {mode === 'login' ? (
            <span className={styles.footerText}>
              حساب کاربری ندارید؟{' '}
              <button type="button" onClick={() => setMode('register')} className={styles.link}>
                ثبت نام
              </button>
            </span>
          ) : (
            <span className={styles.footerText}>
              قبلا ثبت نام کرده‌اید؟{' '}
              <button type="button" onClick={() => setMode('login')} className={styles.link}>
                ورود
              </button>
            </span>
          )}
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
