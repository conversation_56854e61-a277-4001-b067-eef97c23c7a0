/* AuthForm.module.css - styled like LoginForm.module.css */
.container {
  min-height: calc(100vh - 200px);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.formCard {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 2.5rem;
  width: 100%;
  max-width: 400px;
  border: 2px solid #d32f2f;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.title {
  font-size: 1.8rem;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 0.5rem;
}

.subtitle {
  color: #666;
  font-size: 1rem;
  line-height: 1.5;
}

.form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.inputGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.label {
  font-weight: 600;
  color: #333;
  font-size: 0.9rem;
}

.input {
  padding: 1rem;
  border: 2px solid #e5e5e5;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.2s;
  text-align: center;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.input:focus {
  outline: none;
  border-color: #d32f2f;
  box-shadow: 0 0 0 3px rgba(211, 47, 47, 0.1);
}

.input:disabled {
  background-color: #f5f5f5;
  cursor: not-allowed;
}

.inputError {
  border-color: #f44336;
}

.error {
  color: #f44336;
  font-size: 0.85rem;
  margin-top: 0.25rem;
}

.submitBtn {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin-top: 1rem;
}

.submitBtn:hover:not(:disabled) {
  background: #b71c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3);
}

.submitBtn:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.footer {
  margin-top: 2rem;
  text-align: center;
}

.footerText {
  font-size: 0.85rem;
  color: #666;
  line-height: 1.5;
}

.link {
  color: #d32f2f;
  text-decoration: none;
  font-weight: 600;
}

.link:hover {
  text-decoration: underline;
}

@media (max-width: 480px) {
  .container {
    padding: 1rem 0.5rem;
  }
  
  .formCard {
    padding: 2rem 1.5rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
}

@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
  }
  
  .formCard {
    background: #1a1a1a;
    border-color: #ff6b6b;
    color: #e5e5e5;
  }
  
  .title {
    color: #ff6b6b;
  }
  
  .subtitle {
    color: #ccc;
  }
  
  .label {
    color: #e5e5e5;
  }
  
  .input {
    background: #2c2c2c;
    border-color: #444;
    color: #e5e5e5;
  }
  
  .input:focus {
    border-color: #ff6b6b;
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
  }
  
  .input:disabled {
    background-color: #333;
  }
  
  .submitBtn {
    background: #ff6b6b;
  }
  
  .submitBtn:hover:not(:disabled) {
    background: #ff5252;
  }
  
  .footerText {
    color: #ccc;
  }
  
  .link {
    color: #ff6b6b;
  }
}
