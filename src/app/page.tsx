import CurrencyExchange from "@/components/CurrencyExchange";
import CurrencyPrices from "@/components/CurrencyPrices";
import FAQAccordion from "@/components/FAQAccordion";
import styles from "./page.module.css";
import Image from "next/image";

export default function Home() {
  return (
    <div className={styles.page}>
      <div className={styles.hero}>
        <div className={styles.heroContent}>
          <div className={styles.heroImage}>
            <Image
              src="/currencies2.png"
              alt="Currencies"
              width={400}
              height={300}
              className={styles.currenciesImage}
            />
          </div>
          <div className={styles.heroText}>
            <h1 className={`${styles.title} fa-text`}>تبادل بدون مرز</h1>
            <p className={`${styles.subtitle} fa-text`}>
              جابجایی بی‌دردسر ارز
            </p>
            {/* <button className={`${styles.dashboardButton} fa-text`}>
              ورود به دشبورد
            </button> */}
          </div>
        </div>
      </div>
      <CurrencyExchange />
      <CurrencyPrices />
      <FAQAccordion />
    </div>
  );
}
