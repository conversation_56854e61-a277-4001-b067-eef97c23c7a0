'use client';

import React, { useState, useEffect, useCallback } from 'react';
import BankCardList from '../../components/BankCardList';
import BankCardForm from '../../components/BankCardForm';
import ConfirmationModal from '../../components/ConfirmationModal';
import ErrorPopup from '../../components/ErrorPopup';
import { BankCard } from '../../types/bank';
import { getBankCards, addBankCard, updateBankCard, deleteBankCard } from '../../services/bankCardService';
import { useAuth } from '../../contexts/AuthContext';
import styles from './page.module.css';

const BankCardsPage: React.FC = () => {
  const [cards, setCards] = useState<BankCard[]>([]);
  const [editingCard, setEditingCard] = useState<BankCard | undefined>(undefined);
  const [showForm, setShowForm] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [cardToDelete, setCardToDelete] = useState<string | null>(null);
  const [errorMessages, setErrorMessages] = useState<string[] | null>(null);
  const { token } = useAuth();

  const handleApiError = (messages: string[]) => {
    setErrorMessages(messages);
  };

  const fetchCards = useCallback(async () => {
    setLoading(true);
    setError(null);
    if (!token) {
      setError('توکن احراز هویت یافت نشد. لطفا وارد شوید.');
      setLoading(false);
      return;
    }
    try {
      const fetchedCards = await getBankCards(token, handleApiError);
      setCards(fetchedCards);
    } catch (err) {
      console.error('Failed to fetch bank cards:', err);
      setError('خطا در بارگذاری کارت‌های بانکی. لطفا دوباره تلاش کنید.');
    } finally {
      setLoading(false);
    }
  }, [token]);

  useEffect(() => {
    fetchCards();
  }, [fetchCards]);

  const handleAddOrUpdateCard = async (card: BankCard | Omit<BankCard, 'id'>) => {
    setError(null);
    if (!token) {
      setError('توکن احراز هویت یافت نشد. لطفا وارد شوید.');
      return;
    }
    try {
      if ('id' in card && card.id) {
        await updateBankCard(card as BankCard & { id: string }, token, handleApiError);
      } else {
        await addBankCard(card as Omit<BankCard, 'id'>, token, handleApiError);
      }
      setShowForm(false);
      setEditingCard(undefined);
      fetchCards();
    } catch (err) {
      console.error('Failed to save bank card:', err);
      setError('خطا در ذخیره کارت بانکی. لطفا دوباره تلاش کنید.');
    }
  };

  const handleDeleteCard = (id: string) => {
    setCardToDelete(id);
    setShowDeleteModal(true);
  };

  const confirmDeleteCard = async () => {
    if (!cardToDelete || !token) {
      setShowDeleteModal(false);
      setCardToDelete(null);
      return;
    }
    setError(null);
    try {
      await deleteBankCard(cardToDelete, token, handleApiError);
      fetchCards();
    } catch (err) {
      console.error('Failed to delete bank card:', err);
      setError('خطا در حذف کارت بانکی. لطفا دوباره تلاش کنید.');
    } finally {
      setShowDeleteModal(false);
      setCardToDelete(null);
    }
  };

  const cancelDeleteCard = () => {
    setShowDeleteModal(false);
    setCardToDelete(null);
  };

  const handleEditClick = (card: BankCard) => {
    setEditingCard(card);
    setShowForm(true);
  };

  const handleCancelForm = () => {
    setShowForm(false);
    setEditingCard(undefined);
  };

  if (loading) {
    return (
      <div className={styles.container}>
        <div className={styles.loading}>
          <div className={styles.loadingSpinner}></div>
          <div className={styles.loadingText}>در حال بارگذاری کارت‌های بانکی...</div>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>مدیریت کارت‌های بانکی</h1>
        <p className={styles.subtitle}>
          کارت‌های بانکی خود را مدیریت کنید و برای تراکنش‌های آتی آماده نگه دارید
        </p>
      </div>

      {error && (
        <div className={styles.error}>
          <div className={styles.errorIcon}>⚠️</div>
          <div className={styles.errorText}>{error}</div>
          <button onClick={fetchCards} className={styles.retryButton}>
            تلاش مجدد
          </button>
        </div>
      )}

      {errorMessages && (
        <ErrorPopup messages={errorMessages} onClose={() => setErrorMessages(null)} />
      )}

      {!showForm && (
        <button onClick={() => setShowForm(true)} className={styles.addButton}>
          <span className={styles.addButtonIcon}>+</span>
          افزودن کارت جدید
        </button>
      )}

      {showForm ? (
        <BankCardForm card={editingCard} onSubmit={handleAddOrUpdateCard} onCancel={handleCancelForm} />
      ) : (
        <BankCardList cards={cards} onEdit={handleEditClick} onDelete={handleDeleteCard} />
      )}

      <ConfirmationModal
        isOpen={showDeleteModal}
        title="حذف کارت بانکی"
        message="آیا از حذف این کارت بانکی اطمینان دارید؟ این عمل قابل بازگشت نیست."
        confirmText="حذف کارت"
        cancelText="لغو"
        onConfirm={confirmDeleteCard}
        onCancel={cancelDeleteCard}
        type="danger"
      />
    </div>
  );
};

export default BankCardsPage;
