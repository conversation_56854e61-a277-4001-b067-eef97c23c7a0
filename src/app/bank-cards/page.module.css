.container {
  min-height: calc(100vh - 200px);
  padding: 2rem 1rem;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.header {
  text-align: center;
  margin-bottom: 3rem;
}

.title {
  font-size: 3rem;
  font-weight: bold;
  color: #d32f2f;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.subtitle {
  font-size: 1.2rem;
  color: #666;
  max-width: 600px;
  margin: 0 auto 2rem;
  line-height: 1.6;
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 300px;
  text-align: center;
  color: #d32f2f;
}

.loadingSpinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #d32f2f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loadingText {
  font-size: 1.1rem;
  font-weight: 500;
}

.error {
  background: #fff;
  border: 2px solid #f44336;
  border-radius: 12px;
  padding: 1.5rem;
  margin: 2rem auto;
  max-width: 500px;
  text-align: center;
  box-shadow: 0 4px 12px rgba(244, 67, 54, 0.1);
}

.errorIcon {
  font-size: 2rem;
  color: #f44336;
  margin-bottom: 1rem;
}

.errorText {
  color: #f44336;
  font-size: 1.1rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.retryButton {
  background: #f44336;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retryButton:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.addButton {
  background: #d32f2f;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 12px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0 auto 2rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.2);
}

.addButton:hover {
  background: #b71c1c;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(211, 47, 47, 0.3);
}

.addButton:active {
  transform: translateY(0);
}

.addButtonIcon {
  font-size: 1.2rem;
}

@media (max-width: 768px) {
  .container {
    padding: 1rem 0.5rem;
  }

  .title {
    font-size: 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .addButton {
    width: 100%;
    justify-content: center;
  }
}

@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
  }

  .title {
    color: #ff6b6b;
  }

  .subtitle {
    color: #ccc;
  }

  .loading {
    color: #ff6b6b;
  }

  .loadingSpinner {
    border-color: #333;
    border-top-color: #ff6b6b;
  }

  .error {
    background: #1a1a1a;
    border-color: #ff6b6b;
  }

  .errorIcon,
  .errorText {
    color: #ff6b6b;
  }

  .retryButton {
    background: #ff6b6b;
  }

  .retryButton:hover {
    background: #ff5252;
  }

  .addButton {
    background: #ff6b6b;
  }

  .addButton:hover {
    background: #ff5252;
  }
}
