import React from 'react';
import styles from '../../components/ContactUs.module.css';
import Header from '../../components/Header';
import Footer from '../../components/Footer';

const ContactUsPage: React.FC = () => {
  return (
    <div className={styles.container}>
      <main className={styles.mainContent}>
        <h1 className={styles.title}>تماس با ما</h1>
        <p className={styles.description}>
          برای هرگونه سوال، پیشنهاد یا انتقاد، می‌توانید از طریق راه‌های ارتباطی زیر با ما در تماس باشید.
          تیم پشتیبانی ما آماده پاسخگویی به شماست.
        </p>

        <div className={styles.contactInfo}>
          <div className={styles.infoItem}>
            <h2 className={styles.subtitle}>آدرس</h2>
            <p>تهران، خیابان ولیعصر، کوچه صرافی، پلاک ۱۲</p>
          </div>
          <div className={styles.infoItem}>
            <h2 className={styles.subtitle}>تلفن</h2>
            <p>+۹۸ ۲۱ ۱۲۳۴ ۵۶۷۸</p>
          </div>
          <div className={styles.infoItem}>
            <h2 className={styles.subtitle}>ایمیل</h2>
            <p><EMAIL></p>
          </div>
          <div className={styles.infoItem}>
            <h2 className={styles.subtitle}>ساعات کاری</h2>
            <p>شنبه تا چهارشنبه: ۹:۰۰ صبح تا ۵:۰۰ عصر</p>
            <p>پنجشنبه: ۹:۰۰ صبح تا ۱:۰۰ ظهر</p>
          </div>
        </div>

        <h2 className={styles.subtitle}>ارسال پیام</h2>
        <form className={styles.contactForm}>
          <div className={styles.formGroup}>
            <label htmlFor="name">نام شما:</label>
            <input type="text" id="name" name="name" required />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="email">ایمیل شما:</label>
            <input type="email" id="email" name="email" required />
          </div>
          <div className={styles.formGroup}>
            <label htmlFor="message">پیام شما:</label>
            <textarea id="message" name="message" rows={5} required></textarea>
          </div>
          <button type="submit" className={styles.submitButton}>ارسال پیام</button>
        </form>
      </main>
    </div>
  );
};

export default ContactUsPage;
