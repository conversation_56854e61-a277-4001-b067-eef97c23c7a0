.page {
  min-height: 100vh;
  padding: 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero {
  text-align: center;
  padding: 3rem 1rem 1rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: 0;
}

.title {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.subtitle {
  font-size: 1.2rem;
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 50vh;
  font-size: 1.2rem;
  color: #666;
}

@media (max-width: 768px) {
  .hero {
    padding: 2rem 1rem 1rem;
  }
  
  .title {
    font-size: 2rem;
  }
  
  .subtitle {
    font-size: 1rem;
  }
}

@media (prefers-color-scheme: dark) {
  .page {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  }
  
  .hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  .loading {
    color: #ccc;
  }
}

.error {
  color: #ffdddd;
  background-color: #dc3545;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  margin-top: 1rem;
  font-size: 0.9rem;
  display: inline-block;
}

.approvalStatus {
  font-size: 1rem;
  margin-top: 1rem;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  background-color: rgba(255, 255, 255, 0.2);
  display: inline-block;
}
