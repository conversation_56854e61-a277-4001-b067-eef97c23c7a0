.page {
  min-height: calc(100vh - 200px);
  padding: 0;
  background: linear-gradient(100deg, #5f7992 0%, #8b8dc4 50%, #ccb3e5 100%);
  padding-bottom: 50px;
}

.hero {
  /* padding: 4rem 2rem; */
  margin-bottom: 2rem;
}

.heroContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
  gap: 4rem;
}

.heroImage {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.currenciesImage {
  max-width: 100%;
  height: auto;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.2));
}

.heroText {
  flex: 1;
  text-align: right;
  color: white;
}

.title {
  font-size: 4rem;
  font-weight: bold;
  color: white;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.subtitle {
  font-size: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.dashboardButton {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 1rem 2rem;
  font-size: 1.1rem;
  font-weight: bold;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
}

.dashboardButton:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Content sections styling */
.page > :not(.hero) {
  /* background: rgba(255, 255, 255, 0.95); */
  /* margin: 2rem; */
  /* border-radius: 20px; */
  padding: 2rem;
  backdrop-filter: blur(10px);
  /* box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); */
}

@media (max-width: 768px) {
  .hero {
    padding: 2rem 1rem;
  }

  .heroContent {
    flex-direction: column;
    gap: 2rem;
    text-align: center;
  }

  .heroText {
    text-align: center;
  }

  .title {
    font-size: 2.5rem;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .dashboardButton {
    padding: 0.8rem 1.5rem;
    font-size: 1rem;
  }

  .currenciesImage {
    width: 300px;
    height: auto;
  }

  .page > :not(.hero) {
    /* margin: 1rem; */
    padding: 1.5rem;
  }
}

@media (prefers-color-scheme: dark) {
  .page {
    background: linear-gradient(135deg, #6B46C1 0%, #7C3AED 50%, #8B5CF6 100%);
  }

  .title {
    color: white;
  }

  .subtitle {
    color: rgba(255, 255, 255, 0.9);
  }

  .dashboardButton {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.25);
  }

  .dashboardButton:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
  }
}
